<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SIGMA - Administration des Utilisateurs</title>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
    <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-functions-compat.js"></script>
    <style>
      <?!= include('../../css/admin/users.css'); ?>
      <?!= include('../../css/common.css'); ?>
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand"><span class="material-icons">inventory_2</span><span>SIGMA Admin</span></div>
            <div class="nav-menu">
                <a href="../dashboard.html" class="nav-link"><span class="material-icons">dashboard</span>Dashboard</a>
                <a href="users.html" class="nav-link active"><span class="material-icons">people</span>Utilisateurs</a>
                <div class="nav-user"><span id="user-name">Admin</span><button id="logout-btn" class="logout-btn"><span class="material-icons">logout</span></button></div>
            </div>
        </div>
    </nav>
    <main class="main-content">
        <div class="container">
            <div class="page-header">
                <h1><span class="material-icons">people</span>Gestion des Utilisateurs</h1>
                <p class="page-description">Gérez les utilisateurs, leurs rôles et leurs permissions dans SIGMA</p>
            </div>
            <div class="filters-section">
                <div class="search-box"><span class="material-icons">search</span><input type="text" id="search-input" placeholder="Rechercher par email ou nom..."></div>
                <div class="filter-controls">
                    <select id="role-filter"><option value="">Tous les rôles</option><option value="admin">Administrateur</option><option value="regisseur">Régisseur</option><option value="utilisateur">Utilisateur</option></select>
                    <button id="refresh-btn" class="btn btn-secondary"><span class="material-icons">refresh</span>Actualiser</button>
                </div>
            </div>
            <div id="loading-state" class="loading-state" style="display: none;"><div class="spinner"></div><p>Chargement des utilisateurs...</p></div>
            <div id="message-container"></div>
            <div class="users-section">
                <div class="users-header"><h2>Utilisateurs</h2><span id="users-count" class="count-badge">0 utilisateurs</span></div>
                <div class="users-table-container"><table id="users-table" class="users-table"><thead><tr><th>Utilisateur</th><th>Email</th><th>Rôle</th><th>Statut</th><th>Dernière activité</th><th>Actions</th></tr></thead><tbody id="users-tbody"></tbody></table></div>
                <div id="pagination" class="pagination" style="display: none;"><button id="prev-page" class="btn btn-secondary" disabled><span class="material-icons">chevron_left</span>Précédent</button><span id="page-info">Page 1</span><button id="next-page" class="btn btn-secondary">Suivant<span class="material-icons">chevron_right</span></button></div>
            </div>
        </div>
    </main>
    <div id="role-modal" class="modal" style="display: none;"><div class="modal-content"><div class="modal-header"><h3>Modifier le rôle utilisateur</h3><button class="modal-close" id="modal-close"><span class="material-icons">close</span></button></div><div class="modal-body"><div class="user-info"><div class="user-avatar"><span class="material-icons">person</span></div><div class="user-details"><h4 id="modal-user-name">Nom utilisateur</h4><p id="modal-user-email"><EMAIL></p></div></div><div class="form-group"><label for="new-role">Nouveau rôle :</label><select id="new-role" class="form-control"><option value="utilisateur">Utilisateur</option><option value="regisseur">Régisseur</option><option value="admin">Administrateur</option></select></div><div class="form-group"><label for="role-reason">Raison du changement (optionnel) :</label><textarea id="role-reason" class="form-control" rows="3" placeholder="Expliquez pourquoi vous modifiez ce rôle..."></textarea></div></div><div class="modal-footer"><button id="cancel-role-change" class="btn btn-secondary">Annuler</button><button id="confirm-role-change" class="btn btn-primary"><span class="material-icons">save</span>Confirmer</button></div></div></div>
    <div id="confirm-modal" class="modal" style="display: none;"><div class="modal-content"><div class="modal-header"><h3>Confirmation</h3><button class="modal-close" id="confirm-modal-close"><span class="material-icons">close</span></button></div><div class="modal-body"><p id="confirm-message">Êtes-vous sûr de vouloir effectuer cette action ?</p></div><div class="modal-footer"><button id="confirm-cancel" class="btn btn-secondary">Annuler</button><button id="confirm-ok" class="btn btn-danger">Confirmer</button></div></div></div>
    <script>
      <?!= include('../../js/firebase-config.js'); ?>
      <?!= include('../../js/auth.js'); ?>
      <?!= include('../../js/admin/users.js'); ?>
    </script>
</body>
</html>
