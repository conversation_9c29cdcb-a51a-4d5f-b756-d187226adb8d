/**
 * Initialisation du dashboard SIGMA
 * Point d'entrée principal pour le dashboard temps-réel
 */

// Variables globales
let dashboardManager = null;
let paginationManager = null;
let dashboardUI = null;

/**
 * Initialiser le dashboard au chargement de la page
 */
document.addEventListener('DOMContentLoaded', async function() {
  console.log('🚀 Initialisation du dashboard SIGMA...');
  
  try {
    // Vérifier que Firebase est chargé
    if (typeof firebase === 'undefined') {
      throw new Error('Firebase n\'est pas chargé');
    }

    // Attendre l'authentification
    await waitForAuth();
    
    // Initialiser les gestionnaires
    await initializeManagers();
    
    // Configurer les événements
    setupEventListeners();
    
    console.log('✅ Dashboard initialisé avec succès');
    
  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation du dashboard:', error);
    showInitializationError(error.message);
  }
});

/**
 * Attendre que l'utilisateur soit authentifié
 */
function waitForAuth() {
  return new Promise((resolve, reject) => {
    const unsubscribe = firebase.auth().onAuthStateChanged(async (user) => {
      unsubscribe();
      
      if (!user) {
        reject(new Error('Utilisateur non authentifié'));
        return;
      }

      try {
        // Vérifier les permissions
        const idTokenResult = await user.getIdTokenResult();
        const userRole = idTokenResult.claims.role;
        
        if (!['regisseur', 'admin'].includes(userRole)) {
          reject(new Error('Permissions insuffisantes pour accéder au dashboard'));
          return;
        }
        
        console.log(`✅ Utilisateur authentifié: ${user.email} (${userRole})`);
        resolve(user);
        
      } catch (error) {
        reject(error);
      }
    });
  });
}

/**
 * Initialiser tous les gestionnaires
 */
async function initializeManagers() {
  console.log('🔧 Initialisation des gestionnaires...');

  try {
    // Vérifier que les classes sont disponibles
    if (typeof PaginationManager === 'undefined') {
      throw new Error('PaginationManager non chargé');
    }
    if (typeof DashboardUI === 'undefined') {
      throw new Error('DashboardUI non chargé');
    }
    if (typeof DashboardManager === 'undefined') {
      throw new Error('DashboardManager non chargé');
    }

    // Initialiser les gestionnaires dans l'ordre
    paginationManager = new PaginationManager();
    dashboardUI = new DashboardUI();
    dashboardManager = new DashboardManager();

    // Rendre les gestionnaires disponibles globalement
    window.dashboardManager = dashboardManager;
    window.paginationManager = paginationManager;
    window.dashboardUI = dashboardUI;

    // Initialiser le dashboard principal
    await dashboardManager.initialize();

    // Vérifier la santé des listeners après initialisation
    setTimeout(() => {
      const health = dashboardManager.checkListenersHealth();
      if (!health.healthy) {
        console.warn('⚠️ Certains listeners ne sont pas actifs:', health);
      }
    }, 2000);

    console.log('✅ Gestionnaires initialisés avec succès');

  } catch (error) {
    console.error('❌ Erreur lors de l\'initialisation des gestionnaires:', error);
    throw error;
  }
}

/**
 * Configurer les événements globaux
 */
function setupEventListeners() {
  console.log('🎯 Configuration des événements...');
  
  // Événement de mise à jour des tableaux
  document.addEventListener('dashboardTableUpdate', handleTableUpdate);
  
  // Événement de déconnexion
  document.addEventListener('beforeunload', cleanup);
  
  // Événements de visibilité de la page (optimisation)
  document.addEventListener('visibilitychange', handleVisibilityChange);
  
  // Événements de redimensionnement (responsive)
  window.addEventListener('resize', debounce(handleResize, 250));
  
  // Raccourcis clavier
  document.addEventListener('keydown', handleKeyboardShortcuts);
  
  console.log('✅ Événements configurés');
}

/**
 * Gérer les mises à jour des tableaux
 */
function handleTableUpdate(event) {
  const { tableId, items, isRealtimeUpdate, stats } = event.detail;
  
  console.log(`📊 Mise à jour tableau ${tableId}:`, {
    items: items.length,
    isRealtimeUpdate,
    stats
  });
  
  // Mettre à jour les statistiques d'affichage
  updateTableStats(tableId, stats);
  
  // Afficher une notification si mise à jour temps-réel
  if (isRealtimeUpdate && items.length > 0) {
    showUpdateNotification(tableId, items.length);
  }
}

/**
 * Gérer les changements de visibilité de la page
 */
function handleVisibilityChange() {
  if (document.hidden) {
    console.log('📱 Page masquée - Pause des listeners');
    // Optionnel: Mettre en pause les listeners pour économiser les ressources
  } else {
    console.log('📱 Page visible - Reprise des listeners');
    // Optionnel: Reprendre les listeners et rafraîchir les données
    if (dashboardManager && dashboardManager.isInitialized) {
      dashboardManager.refreshAllTables();
    }
  }
}

/**
 * Gérer le redimensionnement de la fenêtre
 */
function handleResize() {
  console.log('📐 Redimensionnement de la fenêtre');
  
  // Ajuster la taille des tableaux si nécessaire
  adjustTableSizes();
  
  // Recalculer la pagination visible
  if (paginationManager) {
    // Logique de recalcul si nécessaire
  }
}

/**
 * Gérer les raccourcis clavier
 */
function handleKeyboardShortcuts(event) {
  // Ctrl/Cmd + R : Rafraîchir le dashboard
  if ((event.ctrlKey || event.metaKey) && event.key === 'r') {
    event.preventDefault();
    refreshDashboard();
  }
  
  // Échap : Fermer les modals/notifications
  if (event.key === 'Escape') {
    closeAllModals();
  }
}

/**
 * Rafraîchir tout le dashboard
 */
function refreshDashboard() {
  console.log('🔄 Rafraîchissement manuel du dashboard');
  
  if (dashboardManager && dashboardManager.isInitialized) {
    dashboardManager.loadInitialData();
    showRefreshNotification();
  }
}

/**
 * Mettre à jour les statistiques d'un tableau
 */
function updateTableStats(tableId, stats) {
  const statsContainer = document.getElementById(`${tableId}-stats`);
  if (!statsContainer || !stats) return;
  
  statsContainer.innerHTML = `
    <div class="table-stats">
      <span class="stat-item">📄 Page ${stats.currentPage}</span>
      <span class="stat-item">📊 ${stats.totalItems} éléments</span>
      ${stats.hasMore ? '<span class="stat-item">➕ Plus disponible</span>' : ''}
      ${stats.isLoading ? '<span class="stat-item">⏳ Chargement...</span>' : ''}
    </div>
  `;
}

/**
 * Afficher une notification de mise à jour
 */
function showUpdateNotification(tableId, itemCount) {
  const notification = document.createElement('div');
  notification.className = 'update-notification';
  notification.innerHTML = `
    <div class="notification-content">
      🔄 ${itemCount} nouvel${itemCount > 1 ? 'les' : 'le'} mise${itemCount > 1 ? 's' : ''} à jour
    </div>
  `;
  
  document.body.appendChild(notification);
  
  // Animation d'apparition
  setTimeout(() => notification.classList.add('show'), 100);
  
  // Suppression automatique après 3 secondes
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 3000);
}

/**
 * Afficher une notification de rafraîchissement
 */
function showRefreshNotification() {
  const notification = document.createElement('div');
  notification.className = 'refresh-notification';
  notification.innerHTML = '🔄 Dashboard rafraîchi';
  
  document.body.appendChild(notification);
  
  setTimeout(() => notification.classList.add('show'), 100);
  setTimeout(() => {
    notification.classList.remove('show');
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }, 2000);
}

/**
 * Afficher une erreur d'initialisation
 */
function showInitializationError(message) {
  const errorContainer = document.getElementById('dashboard-container') || document.body;
  
  errorContainer.innerHTML = `
    <div class="initialization-error">
      <div class="error-icon">❌</div>
      <h2>Erreur d'initialisation</h2>
      <p>${message}</p>
      <button onclick="location.reload()" class="btn btn-primary">
        🔄 Recharger la page
      </button>
    </div>
  `;
}

/**
 * Ajuster la taille des tableaux (responsive)
 */
function adjustTableSizes() {
  const tables = document.querySelectorAll('.dashboard-table');
  const isMobile = window.innerWidth < 768;
  
  tables.forEach(table => {
    if (isMobile) {
      table.classList.add('mobile-view');
    } else {
      table.classList.remove('mobile-view');
    }
  });
}

/**
 * Fermer tous les modals et notifications
 */
function closeAllModals() {
  const modals = document.querySelectorAll('.modal, .notification');
  modals.forEach(modal => {
    modal.classList.remove('show');
  });
}

/**
 * Fonction de debouncing
 */
function debounce(func, wait) {
  let timeout;
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout);
      func(...args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

/**
 * Nettoyer les ressources avant fermeture
 */
function cleanup() {
  console.log('🧹 Nettoyage avant fermeture...');
  
  if (dashboardManager) {
    dashboardManager.cleanup();
  }
  
  if (paginationManager) {
    paginationManager.cleanup();
  }
}

// Fonctions utilitaires globales
window.refreshDashboard = refreshDashboard;
window.cleanup = cleanup;
