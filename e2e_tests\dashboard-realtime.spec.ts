import { test, expect } from '@playwright/test';

/**
 * Tests E2E pour le dashboard SIGMA temps-réel
 * Validation de l'affichage des 7 tableaux et de la connectivité Firestore
 */

test.describe('Dashboard SIGMA - Connectivité Temps-Réel', () => {
  
  test.beforeEach(async ({ page }) => {
    // Aller au dashboard
    await page.goto('/html/dashboard.html');
    
    // Attendre que la page soit chargée
    await page.waitForLoadState('networkidle');
  });

  test('Épique E-3 - Affichage des 7 tableaux du dashboard', async ({ page }) => {
    // Vérifier que tous les conteneurs de tableaux existent
    const tableContainers = [
      'stock-alerts-table',
      'missing-material-table', 
      'overdue-emprunts-table',
      'upcoming-emprunts-table',
      'non-op-modules-table',
      'non-op-material-table',
      'pending-emprunts-table'
    ];

    for (const containerId of tableContainers) {
      await expect(page.locator(`#${containerId}`)).toBeVisible();
    }
  });

  test('Épique E-3 - Chargement des données en moins de 2 secondes', async ({ page }) => {
    const startTime = Date.now();
    
    // Attendre que l'indicateur de chargement disparaisse
    await page.waitForSelector('.loading-state', { state: 'hidden', timeout: 2000 });
    
    const loadTime = Date.now() - startTime;
    expect(loadTime).toBeLessThan(2000);
  });

  test('Épique E-3 - Affichage des métriques de performance', async ({ page }) => {
    // Vérifier que les métriques de performance sont affichées
    await expect(page.locator('#performance-metrics')).toBeVisible();
    
    // Vérifier le contenu des métriques
    const metricsText = await page.locator('#performance-metrics').textContent();
    expect(metricsText).toMatch(/\d+ms/); // Temps d'exécution
    expect(metricsText).toMatch(/\d+ requêtes/); // Nombre de requêtes
  });

  test('Épique E-3 - Indicateur de temps-réel actif', async ({ page }) => {
    // Vérifier que l'indicateur de temps-réel est présent
    await expect(page.locator('#refresh-indicator')).toBeVisible();
    
    // Vérifier le texte initial
    const indicatorText = await page.locator('#refresh-indicator').textContent();
    expect(indicatorText).toContain('Temps-réel actif');
  });

  test('Épique E-3 - Fonctionnement des boutons de rafraîchissement', async ({ page }) => {
    // Attendre que les tableaux soient chargés
    await page.waitForTimeout(1000);
    
    // Cliquer sur un bouton refresh (s'il existe)
    const refreshButton = page.locator('.btn-refresh').first();
    if (await refreshButton.isVisible()) {
      await refreshButton.click();
      
      // Vérifier que l'indicateur se met à jour
      await expect(page.locator('#refresh-indicator')).toContainText('Mis à jour');
    }
  });

  test('Épique E-3 - Gestion des états vides', async ({ page }) => {
    // Vérifier que les états vides sont gérés correctement
    const emptyStates = page.locator('.empty-state');
    
    // Si des états vides sont présents, vérifier leur contenu
    const count = await emptyStates.count();
    if (count > 0) {
      for (let i = 0; i < count; i++) {
        const emptyState = emptyStates.nth(i);
        await expect(emptyState).toBeVisible();
        await expect(emptyState.locator('h4')).toBeVisible();
        await expect(emptyState.locator('p')).toBeVisible();
      }
    }
  });

  test('Épique E-3 - Responsive design des tableaux', async ({ page }) => {
    // Tester sur mobile
    await page.setViewportSize({ width: 375, height: 667 });
    await page.waitForTimeout(500);
    
    // Vérifier que les tableaux s'adaptent
    const tables = page.locator('.dashboard-table');
    const count = await tables.count();
    
    if (count > 0) {
      for (let i = 0; i < count; i++) {
        const table = tables.nth(i);
        await expect(table).toBeVisible();
      }
    }
    
    // Revenir au desktop
    await page.setViewportSize({ width: 1280, height: 720 });
  });

  test('Épique E-3 - Gestion des erreurs de connexion', async ({ page }) => {
    // Simuler une perte de connexion
    await page.context().setOffline(true);
    await page.waitForTimeout(1000);
    
    // Vérifier qu'aucune erreur critique n'est affichée
    const errorContainer = page.locator('#error-container');
    
    // Rétablir la connexion
    await page.context().setOffline(false);
    await page.waitForTimeout(1000);
  });

  test('Épique E-3 - Navigation et raccourcis clavier', async ({ page }) => {
    // Tester le raccourci Ctrl+R pour rafraîchir
    await page.keyboard.press('Control+r');
    
    // Vérifier que le dashboard se rafraîchit (sans recharger la page)
    await expect(page.locator('#refresh-indicator')).toBeVisible();
    
    // Tester la touche Échap
    await page.keyboard.press('Escape');
  });

  test('Épique E-3 - Authentification et permissions', async ({ page }) => {
    // Vérifier que l'utilisateur est connecté
    await expect(page.locator('#user-info')).toBeVisible();
    
    // Vérifier le bouton de déconnexion
    await expect(page.locator('#logout-btn-dashboard')).toBeVisible();
  });

  test('Épique E-3 - Performance et optimisation', async ({ page }) => {
    // Mesurer le temps de chargement initial
    const startTime = Date.now();
    
    // Attendre que tous les tableaux soient initialisés
    await page.waitForFunction(() => {
      return window.dashboardManager && window.dashboardManager.isInitialized;
    }, { timeout: 5000 });
    
    const initTime = Date.now() - startTime;
    expect(initTime).toBeLessThan(3000); // Moins de 3 secondes
    
    // Vérifier qu'il n'y a pas de fuites mémoire évidentes
    const jsHeapSize = await page.evaluate(() => {
      return (performance as any).memory?.usedJSHeapSize || 0;
    });
    
    // Limite raisonnable pour l'utilisation mémoire (50MB)
    expect(jsHeapSize).toBeLessThan(50 * 1024 * 1024);
  });

  test('Épique E-3 - Validation des données temps-réel', async ({ page }) => {
    // Attendre l'initialisation complète
    await page.waitForFunction(() => {
      return window.dashboardManager && window.dashboardManager.isInitialized;
    });
    
    // Vérifier que les listeners sont actifs
    const listenersHealth = await page.evaluate(() => {
      return window.dashboardManager.checkListenersHealth();
    });
    
    expect(listenersHealth.activeCount).toBeGreaterThan(0);
    expect(listenersHealth.failedCount).toBe(0);
  });

  test('Épique E-3 - Intégration avec Firebase', async ({ page }) => {
    // Vérifier que Firebase est chargé
    await page.waitForFunction(() => {
      return typeof window.firebase !== 'undefined';
    });
    
    // Vérifier que les services Firebase sont disponibles
    const firebaseServices = await page.evaluate(() => {
      return {
        auth: typeof window.firebase.auth === 'function',
        firestore: typeof window.firebase.firestore === 'function',
        functions: typeof window.firebase.functions === 'function'
      };
    });
    
    expect(firebaseServices.auth).toBe(true);
    expect(firebaseServices.firestore).toBe(true);
    expect(firebaseServices.functions).toBe(true);
  });

  test('Épique E-3 - Validation de la structure HTML', async ({ page }) => {
    // Vérifier la structure générale du dashboard
    await expect(page.locator('.dashboard-header')).toBeVisible();
    await expect(page.locator('.dashboard-container')).toBeVisible();
    await expect(page.locator('.dashboard-grid')).toBeVisible();

    // Vérifier que toutes les cartes sont présentes
    const cards = page.locator('.dashboard-card');
    const cardCount = await cards.count();
    expect(cardCount).toBe(7); // 7 tableaux attendus

    // Vérifier que chaque carte a un conteneur de tableau
    for (let i = 0; i < cardCount; i++) {
      const card = cards.nth(i);
      await expect(card.locator('.table-container')).toBeVisible();
    }
  });

  test('Épique E-3 - Interactions utilisateur avec les tableaux', async ({ page }) => {
    // Attendre l'initialisation
    await page.waitForTimeout(2000);

    // Tester les boutons d'action s'ils sont présents
    const actionButtons = page.locator('.btn-sm');
    const buttonCount = await actionButtons.count();

    if (buttonCount > 0) {
      // Cliquer sur le premier bouton d'action
      await actionButtons.first().click();

      // Vérifier qu'aucune erreur JavaScript n'est levée
      const errors = await page.evaluate(() => window.testErrors || []);
      expect(errors.length).toBe(0);
    }
  });

  test('Épique E-3 - Mise à jour en temps-réel simulée', async ({ page }) => {
    // Attendre l'initialisation
    await page.waitForFunction(() => {
      return window.dashboardManager && window.dashManager.isInitialized;
    });

    // Simuler une mise à jour temps-réel
    await page.evaluate(() => {
      if (window.dashboardManager) {
        // Déclencher manuellement une mise à jour
        window.dashboardManager.refreshAllTables();
      }
    });

    // Vérifier que l'indicateur se met à jour
    await expect(page.locator('#refresh-indicator')).toContainText('Mis à jour', { timeout: 3000 });
  });
});
