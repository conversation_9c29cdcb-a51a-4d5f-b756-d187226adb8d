#!/usr/bin/env node

/**
 * Script pour exécuter les tests E2E du dashboard SIGMA
 * Utilise Playwright pour les tests end-to-end
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🎭 Exécution des tests E2E du dashboard SIGMA...\n');

// Configuration Playwright pour le dashboard
const playwrightConfig = {
  testDir: './e2e_tests',
  testMatch: '**/dashboard-realtime.spec.ts',
  timeout: 30000,
  expect: {
    timeout: 5000
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: [
    ['html'],
    ['json', { outputFile: 'test-results/dashboard-e2e-results.json' }]
  ],
  use: {
    baseURL: 'http://127.0.0.1:8080',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure'
  },
  projects: [
    {
      name: 'chromium',
      use: { ...require('@playwright/test').devices['Desktop Chrome'] }
    },
    {
      name: 'firefox',
      use: { ...require('@playwright/test').devices['Desktop Firefox'] }
    },
    {
      name: 'webkit',
      use: { ...require('@playwright/test').devices['Desktop Safari'] }
    }
  ],
  webServer: {
    command: 'npx http-server ./src -p 8080 --cors',
    port: 8080,
    reuseExistingServer: !process.env.CI
  }
};

// Créer le fichier de configuration Playwright temporaire
const configPath = path.join(__dirname, '..', 'playwright.dashboard.config.ts');
const configContent = `import { defineConfig } from '@playwright/test';

export default defineConfig(${JSON.stringify(playwrightConfig, null, 2)});`;

try {
  // Vérifier que Playwright est installé
  try {
    execSync('npx playwright --version', { stdio: 'pipe' });
  } catch (error) {
    console.log('📦 Installation de Playwright...');
    execSync('npx playwright install', { stdio: 'inherit' });
  }
  
  // Écrire la configuration
  fs.writeFileSync(configPath, configContent);
  
  // Créer le dossier de résultats s'il n'existe pas
  const resultsDir = path.join(__dirname, '..', 'test-results');
  if (!fs.existsSync(resultsDir)) {
    fs.mkdirSync(resultsDir, { recursive: true });
  }
  
  console.log('🚀 Démarrage du serveur de test...');
  console.log('🎯 URL de test: http://127.0.0.1:8080');
  console.log('📋 Tests: dashboard-realtime.spec.ts\n');
  
  // Exécuter Playwright
  execSync(`npx playwright test --config=${configPath}`, {
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  
  console.log('\n✅ Tous les tests E2E sont passés avec succès !');
  console.log('📊 Rapport disponible dans playwright-report/index.html');
  
} catch (error) {
  console.error('\n❌ Échec des tests E2E:', error.message);
  
  // Afficher les informations de debug
  console.log('\n🔍 Informations de debug:');
  console.log('- Vérifiez que le serveur HTTP est accessible');
  console.log('- Consultez les captures d\'écran dans test-results/');
  console.log('- Vérifiez les logs dans playwright-report/');
  
  process.exit(1);
  
} finally {
  // Nettoyer le fichier de configuration temporaire
  if (fs.existsSync(configPath)) {
    fs.unlinkSync(configPath);
  }
}
