/**
 * Tests d'intégration pour le dashboard SIGMA
 * Validation de l'initialisation et de la connectivité temps-réel
 */

describe('Dashboard Integration', () => {
  let mockFirebase;
  let mockAuth;
  let mockFirestore;
  let mockUser;

  beforeEach(() => {
    // Mock user avec permissions
    mockUser = {
      uid: 'test-user-id',
      email: '<EMAIL>',
      getIdTokenResult: jest.fn().mockResolvedValue({
        claims: { role: 'regisseur' }
      })
    };

    // Mock Firebase Auth
    mockAuth = {
      currentUser: mockUser,
      onAuthStateChanged: jest.fn((callback) => {
        callback(mockUser);
        return jest.fn(); // unsubscribe function
      })
    };

    // Mock Firestore
    mockFirestore = {
      collection: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      onSnapshot: jest.fn((successCallback) => {
        // Simuler des données de test
        const mockSnapshot = {
          docs: [
            {
              id: 'stock1',
              data: () => ({
                nom: 'Test Stock',
                quantite: 5,
                seuilAlerte: 10,
                estOperationnel: true
              })
            }
          ]
        };
        
        // Appeler le callback avec des données de test
        setTimeout(() => successCallback(mockSnapshot), 100);
        
        return jest.fn(); // unsubscribe function
      })
    };

    // Mock Firebase global
    global.firebase = {
      auth: () => mockAuth,
      firestore: () => mockFirestore,
      functions: () => ({
        httpsCallable: jest.fn().mockReturnValue(
          jest.fn().mockResolvedValue({
            data: {
              stockAlerts: [],
              missingMaterial: [],
              overdueEmprunts: [],
              upcomingEmprunts: [],
              nonOpModules: [],
              nonOpMaterial: [],
              pendingEmprunts: [],
              timestamp: new Date().toISOString(),
              performance: {
                totalQueries: 7,
                executionTimeMs: 150
              }
            }
          })
        )
      })
    };

    // Mock des classes globales
    global.PaginationManager = jest.fn().mockImplementation(() => ({
      initializePagination: jest.fn(),
      cleanup: jest.fn()
    }));

    global.DashboardUI = jest.fn().mockImplementation(() => ({
      updateStockAlertsTable: jest.fn(),
      updateMissingMaterialTable: jest.fn(),
      updateOverdueEmpruntsTable: jest.fn(),
      updateUpcomingEmpruntsTable: jest.fn(),
      updateNonOpModulesTable: jest.fn(),
      updateNonOpMaterialTable: jest.fn(),
      updatePendingEmpruntsTable: jest.fn()
    }));

    // Mock DOM elements
    document.getElementById = jest.fn((id) => {
      const mockElement = {
        innerHTML: '',
        className: '',
        addEventListener: jest.fn(),
        querySelector: jest.fn(),
        querySelectorAll: jest.fn(() => [])
      };
      return mockElement;
    });

    document.addEventListener = jest.fn();
    document.querySelectorAll = jest.fn(() => []);
  });

  afterEach(() => {
    jest.clearAllMocks();
    // Nettoyer les variables globales
    delete global.dashboardManager;
    delete global.dashboardUI;
    delete global.paginationManager;
  });

  describe('Initialisation complète', () => {
    it('devrait initialiser tous les gestionnaires avec succès', async () => {
      // Charger le script d'initialisation
      require('../dashboard-init.js');

      // Simuler l'événement DOMContentLoaded
      const domContentLoadedCallback = document.addEventListener.mock.calls
        .find(call => call[0] === 'DOMContentLoaded')[1];

      await domContentLoadedCallback();

      // Vérifier que les gestionnaires sont créés
      expect(global.PaginationManager).toHaveBeenCalled();
      expect(global.DashboardUI).toHaveBeenCalled();
      expect(global.DashboardManager).toHaveBeenCalled();

      // Vérifier que les instances sont disponibles globalement
      expect(global.dashboardManager).toBeDefined();
      expect(global.dashboardUI).toBeDefined();
      expect(global.paginationManager).toBeDefined();
    });

    it('devrait gérer les erreurs d\'authentification', async () => {
      // Mock user sans permissions
      mockAuth.onAuthStateChanged = jest.fn((callback) => {
        callback(null); // Pas d'utilisateur
        return jest.fn();
      });

      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      require('../dashboard-init.js');
      const domContentLoadedCallback = document.addEventListener.mock.calls
        .find(call => call[0] === 'DOMContentLoaded')[1];

      await expect(domContentLoadedCallback()).rejects.toThrow();
      expect(consoleSpy).toHaveBeenCalled();

      consoleSpy.mockRestore();
    });
  });

  describe('Configuration des listeners', () => {
    it('devrait configurer tous les listeners Firestore', async () => {
      const dashboardManager = new DashboardManager();
      dashboardManager.dashboardUI = new DashboardUI();
      
      await dashboardManager.initialize();

      // Vérifier que les requêtes Firestore sont configurées
      expect(mockFirestore.collection).toHaveBeenCalledWith('stocks');
      expect(mockFirestore.collection).toHaveBeenCalledWith('emprunts');
      expect(mockFirestore.collection).toHaveBeenCalledWith('modules');

      // Vérifier que les listeners sont enregistrés
      expect(dashboardManager.listeners.size).toBeGreaterThan(0);
    });

    it('devrait appeler les fonctions UI lors des mises à jour', (done) => {
      const dashboardManager = new DashboardManager();
      const mockUI = new DashboardUI();
      dashboardManager.dashboardUI = mockUI;
      dashboardManager.isInitialized = true;

      // Configurer un listener
      dashboardManager.setupStockAlertsListener();

      // Attendre que le callback soit appelé
      setTimeout(() => {
        expect(mockUI.updateStockAlertsTable).toHaveBeenCalled();
        done();
      }, 200);
    });
  });

  describe('Gestion d\'erreur et retry', () => {
    it('devrait retry automatiquement en cas d\'erreur', () => {
      const dashboardManager = new DashboardManager();
      const setTimeoutSpy = jest.spyOn(global, 'setTimeout');

      dashboardManager.handleError('test-listener', new Error('Connection failed'));

      expect(setTimeoutSpy).toHaveBeenCalled();
      setTimeoutSpy.mockRestore();
    });

    it('devrait arrêter après le nombre maximum de retries', () => {
      const dashboardManager = new DashboardManager();
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

      // Simuler plusieurs échecs
      dashboardManager.retryCounters = new Map();
      dashboardManager.retryCounters.set('test-listener', 3);

      dashboardManager.handleError('test-listener', new Error('Persistent error'));

      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Échec définitif')
      );
      consoleSpy.mockRestore();
    });
  });

  describe('Performance et monitoring', () => {
    it('devrait monitorer les performances des requêtes', () => {
      const dashboardManager = new DashboardManager();
      const startTime = Date.now() - 100;

      const metrics = dashboardManager.monitorQueryPerformance('stockAlerts', startTime, 5);

      expect(metrics.totalQueries).toBe(1);
      expect(metrics.averageDuration).toBeGreaterThan(0);
      expect(metrics.itemCounts).toEqual([5]);
    });

    it('devrait vérifier la santé des listeners', () => {
      const dashboardManager = new DashboardManager();
      
      // Ajouter quelques listeners
      dashboardManager.listeners.set('stockAlerts', jest.fn());
      dashboardManager.listeners.set('missingMaterial', jest.fn());

      const health = dashboardManager.checkListenersHealth();

      expect(health.activeCount).toBe(2);
      expect(health.expectedCount).toBe(7);
      expect(health.healthy).toBe(false);
    });
  });

  describe('Nettoyage des ressources', () => {
    it('devrait nettoyer tous les listeners lors du cleanup', () => {
      const dashboardManager = new DashboardManager();
      const mockUnsubscribe = jest.fn();
      
      dashboardManager.listeners.set('test1', mockUnsubscribe);
      dashboardManager.listeners.set('test2', mockUnsubscribe);

      dashboardManager.cleanup();

      expect(mockUnsubscribe).toHaveBeenCalledTimes(2);
      expect(dashboardManager.listeners.size).toBe(0);
    });
  });
});
