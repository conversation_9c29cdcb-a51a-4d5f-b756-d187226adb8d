<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>SIGMA - Dashboard</title>
  
  <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-app-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-auth-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-firestore-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-functions-compat.js"></script>
  <script src="https://www.gstatic.com/firebasejs/9.23.0/firebase-storage-compat.js"></script>
  
  <style>
    <?!= include('css/common.css'); ?>
    <?!= include('css/dashboard.css'); ?>
  </style>
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet">
</head>
<body>
  <header class="dashboard-header">
    <div class="header-content">
      <div class="header-left">
        <h1>📊 Dashboard SIGMA</h1>
        <div class="refresh-indicator" id="refresh-indicator">🔄 Temps-réel actif</div>
      </div>
      <div class="header-right">
        <div class="performance-metrics" id="performance-metrics"></div>
        <div class="user-info" id="user-info">
          <span id="user-name">Utilisateur</span>
          <button class="btn btn-outline" id="logout-btn-dashboard">Déconnexion</button>
        </div>
      </div>
    </div>
  </header>
  <main class="dashboard-container">
    <div class="dashboard-grid">
      <div class="dashboard-card critical" id="stock-alerts">
        <div class="card-content">
          <h4>🚨 Alertes Stock</h4>
          <div class="card-body">
            <p>Articles dont le stock est critique ou proche de l'épuisement</p>
          </div>
        </div>
      </div>
      <div class="dashboard-card" id="missing-material">
        <div class="card-content">
          <h4>❌ Matériel spécifique manquant</h4>
          <div class="card-body">
            <p>Matériels spécifiques qui doivent être commandés</p>
          </div>
        </div>
      </div>
      <div class="dashboard-card critical" id="overdue-emprunts">
        <div class="card-content">
          <h4>🚨 Emprunts non revenus</h4>
          <div class="card-body">
            <p>Emprunts sortis mais pas encore retournés</p>
          </div>
        </div>
      </div>
      <div class="dashboard-card info" id="upcoming-emprunts">
        <div class="card-content">
          <h4>📅 Prochains emprunts</h4>
          <div class="card-body">
            <p>Départ dans moins de 30 jours ou date de départ passée</p>
          </div>
        </div>
      </div>
      <div class="dashboard-card warning" id="non-op-modules">
        <div class="card-content">
          <h4>📌 Modules non opérationnels</h4>
          <div class="card-body">
            <p>Malles à réassortir</p>
          </div>
        </div>
      </div>
      <div class="dashboard-card warning" id="non-op-material">
        <div class="card-content">
          <h4>🔧 Matériel non opérationnel</h4>
          <div class="card-body">
            <p>Matériel dégradé ou en réparation</p>
          </div>
        </div>
      </div>
      <div class="dashboard-card" id="pending-emprunts">
        <div class="card-content">
          <h4>⏳ Emprunts en attente</h4>
          <div class="card-body">
            <p>Emprunts en attente d'inventaire et/ou de facturation</p>
          </div>
        </div>
      </div>
    </div>
  </main>
  <script>
    <?!= include('js/firebase-config.js'); ?>
    <?!= include('js/auth.js'); ?>
    <?!= include('js/dashboard/PaginationManager.js'); ?>
    <?!= include('js/dashboard/DashboardUI.js'); ?>
    <?!= include('js/dashboard/DashboardManager.js'); ?>
    <?!= include('js/dashboard/dashboard-init.js'); ?>

    document.getElementById('logout-btn-dashboard').addEventListener('click', () => {
        if (window.authManager) {
            window.authManager.signOut();
        }
    });
  </script>
</body>
</html>
