{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/utils/auth.ts"], "names": [], "mappings": ";;AAgBA,kCAiBC;AAED,kCAUC;AAED,kDASC;AAED,4DAWC;AAjED,uDAAyD;AACzD,2DAA4C;AAW5C,SAAgB,WAAW,CAAC,OAAwB;IAClD,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,CAAC;QAClB,2BAAM,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;QAClD,MAAM,IAAI,kBAAU,CAAC,iBAAiB,EAAE,0BAA0B,CAAC,CAAC;IACtE,CAAC;IACD,MAAM,EAAE,GAAG,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC,IAAI,CAAC;IACpC,MAAM,IAAI,GAAG,KAAK,CAAC,IAAgB,CAAC;IACpC,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC,OAAO,EAAE,WAAW,EAAE,aAAa,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE,CAAC;QACnE,2BAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,CAAC,CAAC;QACrE,MAAM,IAAI,kBAAU,CAAC,mBAAmB,EAAE,wCAAwC,CAAC,CAAC;IACtF,CAAC;IACD,OAAO;QACL,GAAG;QACH,KAAK,EAAE,KAAK,CAAC,KAAK,IAAI,KAAK;QAC3B,IAAI;QACJ,WAAW,EAAE,KAAK,CAAC,IAAI;KACxB,CAAC;AACJ,CAAC;AAED,SAAgB,WAAW,CAAC,OAAwB,EAAE,YAAsB;IAC1E,MAAM,IAAI,GAAG,WAAW,CAAC,OAAO,CAAC,CAAC;IAClC,MAAM,aAAa,GAA6B,EAAE,aAAa,EAAE,CAAC,EAAE,WAAW,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;IAEjG,IAAI,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,aAAa,CAAC,YAAY,CAAC,EAAE,CAAC;QAC3D,2BAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,EAAE,GAAG,EAAE,IAAI,CAAC,GAAG,EAAE,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,YAAY,EAAE,CAAC,CAAC;QACrG,wFAAwF;QACxF,MAAM,IAAI,kBAAU,CAAC,mBAAmB,EAAE,8BAA8B,YAAY,EAAE,CAAC,CAAC;IAC1F,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED,SAAgB,mBAAmB,CAAC,IAAuB,EAAE,MAAc,EAAE,QAAiB,EAAE,cAAoC;IAClI,2BAAM,CAAC,IAAI,CAAC,gBAAgB,kBAC1B,GAAG,EAAE,IAAI,CAAC,GAAG,EACb,KAAK,EAAE,IAAI,CAAC,KAAK,EACjB,IAAI,EAAE,IAAI,CAAC,IAAI,EACf,MAAM;QACN,QAAQ,IACL,cAAc,EACjB,CAAC;AACL,CAAC;AAED,SAAgB,wBAAwB,CAAI,IAAS,EAAE,SAA2B,EAAE,OAAwB;;IAC1G,IAAI,CAAC;QACH,OAAO,SAAS,CAAC,IAAI,CAAC,CAAC;IACzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,2BAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE;YACzC,GAAG,EAAE,MAAA,OAAO,CAAC,IAAI,0CAAE,GAAG;YACtB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,iBAAiB;YACjE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;SAC3B,CAAC,CAAC;QACH,MAAM,IAAI,kBAAU,CAAC,kBAAkB,EAAE,6BAA6B,CAAC,CAAC;IAC1E,CAAC;AACH,CAAC"}