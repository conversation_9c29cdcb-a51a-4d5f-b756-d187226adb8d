{"config": {"configFile": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "forbidOnly": false, "fullyParallel": false, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"actualWorkers": 1}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results.json"}], ["junit", {"outputFile": "test-results.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/test-results", "repeatEach": 1, "retries": 1, "metadata": {"actualWorkers": 1}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 60000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 1, "webServer": null}, "suites": [{"title": "dashboard-epic3.spec.ts", "file": "dashboard-epic3.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Dashboard SIGMA - Épique E-3 (Google Apps Script)", "file": "dashboard-epic3.spec.ts", "line": 18, "column": 6, "specs": [{"title": "1. Navigation vers le dashboard", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 21455, "error": {"message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/SIGMA/i\u001b[39m\nReceived string:  \u001b[31m\"Apps Script  |  Google for Developers\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 15000ms\u001b[22m\n\u001b[2m    18 × unexpected value \"Apps Script  |  Google for Developers\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/SIGMA/i\u001b[39m\nReceived string:  \u001b[31m\"Apps Script  |  Google for Developers\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 15000ms\u001b[22m\n\u001b[2m    18 × unexpected value \"Apps Script  |  Google for Developers\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:44:24", "location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 24, "line": 44}, "snippet": "\u001b[0m \u001b[90m 42 |\u001b[39m\n \u001b[90m 43 |\u001b[39m     \u001b[90m// Vérifier que la page se charge correctement\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 44 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/SIGMA/i\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 45 |\u001b[39m\n \u001b[90m 46 |\u001b[39m     \u001b[90m// Vérifier le titre principal\u001b[39m\n \u001b[90m 47 |\u001b[39m     \u001b[36mconst\u001b[39m mainTitle \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 24, "line": 44}, "message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/SIGMA/i\u001b[39m\nReceived string:  \u001b[31m\"Apps Script  |  Google for Developers\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 15000ms\u001b[22m\n\u001b[2m    18 × unexpected value \"Apps Script  |  Google for Developers\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 42 |\u001b[39m\n \u001b[90m 43 |\u001b[39m     \u001b[90m// Vérifier que la page se charge correctement\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 44 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/SIGMA/i\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 45 |\u001b[39m\n \u001b[90m 46 |\u001b[39m     \u001b[90m// Vérifier le titre principal\u001b[39m\n \u001b[90m 47 |\u001b[39m     \u001b[36mconst\u001b[39m mainTitle \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:44:24\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-08T19:26:45.449Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 24, "line": 44}}, {"workerIndex": 1, "parallelIndex": 0, "status": "failed", "duration": 22279, "error": {"message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/SIGMA/i\u001b[39m\nReceived string:  \u001b[31m\"Apps Script  |  Google for Developers\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 15000ms\u001b[22m\n\u001b[2m    18 × unexpected value \"Apps Script  |  Google for Developers\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/SIGMA/i\u001b[39m\nReceived string:  \u001b[31m\"Apps Script  |  Google for Developers\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 15000ms\u001b[22m\n\u001b[2m    18 × unexpected value \"Apps Script  |  Google for Developers\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:44:24", "location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 24, "line": 44}, "snippet": "\u001b[0m \u001b[90m 42 |\u001b[39m\n \u001b[90m 43 |\u001b[39m     \u001b[90m// Vérifier que la page se charge correctement\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 44 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/SIGMA/i\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 45 |\u001b[39m\n \u001b[90m 46 |\u001b[39m     \u001b[90m// Vérifier le titre principal\u001b[39m\n \u001b[90m 47 |\u001b[39m     \u001b[36mconst\u001b[39m mainTitle \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 24, "line": 44}, "message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mpage\u001b[39m\u001b[2m).\u001b[22mtoHaveTitle\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nExpected pattern: \u001b[32m/SIGMA/i\u001b[39m\nReceived string:  \u001b[31m\"Apps Script  |  Google for Developers\"\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveTitle\" with timeout 15000ms\u001b[22m\n\u001b[2m    18 × unexpected value \"Apps Script  |  Google for Developers\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 42 |\u001b[39m\n \u001b[90m 43 |\u001b[39m     \u001b[90m// Vérifier que la page se charge correctement\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 44 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page)\u001b[33m.\u001b[39mtoHaveTitle(\u001b[35m/SIGMA/i\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 45 |\u001b[39m\n \u001b[90m 46 |\u001b[39m     \u001b[90m// Vérifier le titre principal\u001b[39m\n \u001b[90m 47 |\u001b[39m     \u001b[36mconst\u001b[39m mainTitle \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'h1'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:44:24\u001b[22m"}], "stdout": [], "stderr": [], "retry": 1, "startTime": "2025-08-08T19:27:08.905Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 24, "line": 44}}], "status": "unexpected"}], "id": "130fd2ee41949c9c0fbc-1d3d9dcdb898fe1f4b40", "file": "dashboard-epic3.spec.ts", "line": 40, "column": 7}, {"title": "2. Affichage des 7 tableaux du dashboard", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 0, "status": "failed", "duration": 20973, "error": {"message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.dashboard-card')\nExpected: \u001b[32m7\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('.dashboard-card')\u001b[22m\n\u001b[2m    18 × locator resolved to 0 elements\u001b[22m\n\u001b[2m       - unexpected value \"0\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.dashboard-card')\nExpected: \u001b[32m7\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('.dashboard-card')\u001b[22m\n\u001b[2m    18 × locator resolved to 0 elements\u001b[22m\n\u001b[2m       - unexpected value \"0\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:75:36", "location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 36, "line": 75}, "snippet": "\u001b[0m \u001b[90m 73 |\u001b[39m\n \u001b[90m 74 |\u001b[39m     \u001b[36mtry\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 75 |\u001b[39m       \u001b[36mawait\u001b[39m expect(dashboardCards)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m7\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 76 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Les 7 tableaux du dashboard sont présents'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 77 |\u001b[39m     } \u001b[36mcatch\u001b[39m (error) {\n \u001b[90m 78 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m'❌ Les 7 tableaux du dashboard ne sont pas tous présents'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 36, "line": 75}, "message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.dashboard-card')\nExpected: \u001b[32m7\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('.dashboard-card')\u001b[22m\n\u001b[2m    18 × locator resolved to 0 elements\u001b[22m\n\u001b[2m       - unexpected value \"0\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 73 |\u001b[39m\n \u001b[90m 74 |\u001b[39m     \u001b[36mtry\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 75 |\u001b[39m       \u001b[36mawait\u001b[39m expect(dashboardCards)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m7\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 76 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Les 7 tableaux du dashboard sont présents'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 77 |\u001b[39m     } \u001b[36mcatch\u001b[39m (error) {\n \u001b[90m 78 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m'❌ Les 7 tableaux du dashboard ne sont pas tous présents'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:75:36\u001b[22m"}], "stdout": [{"text": "❌ Les 7 tableaux du dashboard ne sont pas tous présents\n"}, {"text": "Nombre de cartes trouvées: 0/7\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-08T19:27:33.738Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 36, "line": 75}}, {"workerIndex": 3, "parallelIndex": 0, "status": "failed", "duration": 24780, "error": {"message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.dashboard-card')\nExpected: \u001b[32m7\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('.dashboard-card')\u001b[22m\n\u001b[2m    18 × locator resolved to 0 elements\u001b[22m\n\u001b[2m       - unexpected value \"0\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.dashboard-card')\nExpected: \u001b[32m7\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('.dashboard-card')\u001b[22m\n\u001b[2m    18 × locator resolved to 0 elements\u001b[22m\n\u001b[2m       - unexpected value \"0\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:75:36", "location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 36, "line": 75}, "snippet": "\u001b[0m \u001b[90m 73 |\u001b[39m\n \u001b[90m 74 |\u001b[39m     \u001b[36mtry\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 75 |\u001b[39m       \u001b[36mawait\u001b[39m expect(dashboardCards)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m7\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 76 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Les 7 tableaux du dashboard sont présents'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 77 |\u001b[39m     } \u001b[36mcatch\u001b[39m (error) {\n \u001b[90m 78 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m'❌ Les 7 tableaux du dashboard ne sont pas tous présents'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 36, "line": 75}, "message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveCount\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('.dashboard-card')\nExpected: \u001b[32m7\u001b[39m\nReceived: \u001b[31m0\u001b[39m\nCall log:\n\u001b[2m  - Expect \"toHaveCount\" with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('.dashboard-card')\u001b[22m\n\u001b[2m    18 × locator resolved to 0 elements\u001b[22m\n\u001b[2m       - unexpected value \"0\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 73 |\u001b[39m\n \u001b[90m 74 |\u001b[39m     \u001b[36mtry\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 75 |\u001b[39m       \u001b[36mawait\u001b[39m expect(dashboardCards)\u001b[33m.\u001b[39mtoHaveCount(\u001b[35m7\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 76 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m'✅ Les 7 tableaux du dashboard sont présents'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 77 |\u001b[39m     } \u001b[36mcatch\u001b[39m (error) {\n \u001b[90m 78 |\u001b[39m       console\u001b[33m.\u001b[39mlog(\u001b[32m'❌ Les 7 tableaux du dashboard ne sont pas tous présents'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:75:36\u001b[22m"}], "stdout": [{"text": "❌ Les 7 tableaux du dashboard ne sont pas tous présents\n"}, {"text": "Nombre de cartes trouvées: 0/7\n"}], "stderr": [], "retry": 1, "startTime": "2025-08-08T19:27:56.753Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 36, "line": 75}}], "status": "unexpected"}], "id": "130fd2ee41949c9c0fbc-5439dfa9e4a356f454d9", "file": "dashboard-epic3.spec.ts", "line": 67, "column": 7}, {"title": "3. Chargement des données en moins de 5 secondes (Google Apps Script)", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "passed", "duration": 10220, "errors": [], "stdout": [{"text": "❌ Chargement en 5874ms ou métriques non visibles\n"}, {"text": "⚠️ Page chargée mais métriques non visibles\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-08T19:28:24.185Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "130fd2ee41949c9c0fbc-950e490efebfb2000b3e", "file": "dashboard-epic3.spec.ts", "line": 91, "column": 7}, {"title": "4. Affichage des alertes stock critiques", "ok": false, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 20893, "error": {"message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#stock-alerts')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('#stock-alerts')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#stock-alerts')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('#stock-alerts')\u001b[22m\n\n    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:130:40", "location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 40, "line": 130}, "snippet": "\u001b[0m \u001b[90m 128 |\u001b[39m\n \u001b[90m 129 |\u001b[39m     \u001b[36mtry\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 130 |\u001b[39m       \u001b[36mawait\u001b[39m expect(stockAlertsSection)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 131 |\u001b[39m\n \u001b[90m 132 |\u001b[39m       \u001b[90m// Vérifier le contenu des alertes\u001b[39m\n \u001b[90m 133 |\u001b[39m       \u001b[36mconst\u001b[39m alertContent \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#stock-alerts .card-content'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 40, "line": 130}, "message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#stock-alerts')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('#stock-alerts')\u001b[22m\n\n\n\u001b[0m \u001b[90m 128 |\u001b[39m\n \u001b[90m 129 |\u001b[39m     \u001b[36mtry\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 130 |\u001b[39m       \u001b[36mawait\u001b[39m expect(stockAlertsSection)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 131 |\u001b[39m\n \u001b[90m 132 |\u001b[39m       \u001b[90m// Vérifier le contenu des alertes\u001b[39m\n \u001b[90m 133 |\u001b[39m       \u001b[36mconst\u001b[39m alertContent \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#stock-alerts .card-content'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:130:40\u001b[22m"}], "stdout": [{"text": "❌ Section alertes stock non trouvée\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-08T19:28:34.608Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 40, "line": 130}}, {"workerIndex": 5, "parallelIndex": 0, "status": "failed", "duration": 23903, "error": {"message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#stock-alerts')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('#stock-alerts')\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#stock-alerts')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('#stock-alerts')\u001b[22m\n\n    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:130:40", "location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 40, "line": 130}, "snippet": "\u001b[0m \u001b[90m 128 |\u001b[39m\n \u001b[90m 129 |\u001b[39m     \u001b[36mtry\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 130 |\u001b[39m       \u001b[36mawait\u001b[39m expect(stockAlertsSection)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 131 |\u001b[39m\n \u001b[90m 132 |\u001b[39m       \u001b[90m// Vérifier le contenu des alertes\u001b[39m\n \u001b[90m 133 |\u001b[39m       \u001b[36mconst\u001b[39m alertContent \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#stock-alerts .card-content'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 40, "line": 130}, "message": "Error: \u001b[31mTimed out 15000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeVisible\u001b[2m()\u001b[22m\n\nLocator: locator('#stock-alerts')\nExpected: visible\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - Expect \"toBeVisible\" with timeout 15000ms\u001b[22m\n\u001b[2m  - waiting for locator('#stock-alerts')\u001b[22m\n\n\n\u001b[0m \u001b[90m 128 |\u001b[39m\n \u001b[90m 129 |\u001b[39m     \u001b[36mtry\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 130 |\u001b[39m       \u001b[36mawait\u001b[39m expect(stockAlertsSection)\u001b[33m.\u001b[39mtoBeVisible({ timeout\u001b[33m:\u001b[39m \u001b[35m15000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 131 |\u001b[39m\n \u001b[90m 132 |\u001b[39m       \u001b[90m// Vérifier le contenu des alertes\u001b[39m\n \u001b[90m 133 |\u001b[39m       \u001b[36mconst\u001b[39m alertContent \u001b[33m=\u001b[39m page\u001b[33m.\u001b[39mlocator(\u001b[32m'#stock-alerts .card-content'\u001b[39m)\u001b[33m;\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:130:40\u001b[22m"}], "stdout": [{"text": "❌ Section alertes stock non trouvée\n"}], "stderr": [], "retry": 1, "startTime": "2025-08-08T19:28:57.451Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium-retry1\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium-retry1\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium-retry1\\error-context.md"}, {"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium-retry1\\trace.zip"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 40, "line": 130}}], "status": "unexpected"}], "id": "130fd2ee41949c9c0fbc-01981b7c600b38206b46", "file": "dashboard-epic3.spec.ts", "line": 122, "column": 7}, {"title": "5. Gest<PERSON> de l'affichage/masquage des alertes", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "passed", "duration": 20626, "errors": [], "stdout": [{"text": "❌ Section alertes non accessible pour interaction\n"}, {"text": "⚠️ Test ignoré - fonctionnalité en cours d'implémentation\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-08T19:29:24.092Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "130fd2ee41949c9c0fbc-5ac1ab48559e21ae89af", "file": "dashboard-epic3.spec.ts", "line": 150, "column": 7}, {"title": "6. Simulation d'alerte en temps réel (limitée sur Google Apps Script)", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 0, "status": "failed", "duration": 34680, "error": {"message": "TimeoutError: page.waitForLoadState: Timeout 30000ms exceeded.\n=========================== logs ===========================\n  \"load\" event fired\n============================================================", "stack": "TimeoutError: page.waitForLoadState: Timeout 30000ms exceeded.\n=========================== logs ===========================\n  \"load\" event fired\n============================================================\n    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:34:16", "location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 16, "line": 34}, "snippet": "\u001b[0m \u001b[90m 32 |\u001b[39m\n \u001b[90m 33 |\u001b[39m     \u001b[90m// Attendre que la page soit complètement chargée\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m     \u001b[90m// Note: Les intercepteurs réseau sont limités avec Google Apps Script\u001b[39m\n \u001b[90m 37 |\u001b[39m     \u001b[90m// Les tests doivent s'adapter aux données réelles ou utiliser des mécanismes de test intégrés\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 16, "line": 34}, "message": "TimeoutError: page.waitForLoadState: Timeout 30000ms exceeded.\n=========================== logs ===========================\n  \"load\" event fired\n============================================================\n\n\u001b[0m \u001b[90m 32 |\u001b[39m\n \u001b[90m 33 |\u001b[39m     \u001b[90m// Attendre que la page soit complètement chargée\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 34 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mwaitForLoadState(\u001b[32m'networkidle'\u001b[39m\u001b[33m,\u001b[39m { timeout\u001b[33m:\u001b[39m \u001b[35m30000\u001b[39m })\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 35 |\u001b[39m\n \u001b[90m 36 |\u001b[39m     \u001b[90m// Note: Les intercepteurs réseau sont limités avec Google Apps Script\u001b[39m\n \u001b[90m 37 |\u001b[39m     \u001b[90m// Les tests doivent s'adapter aux données réelles ou utiliser des mécanismes de test intégrés\u001b[39m\u001b[0m\n\u001b[2m    at C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:34:16\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-08-08T19:29:44.938Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--85ef3-tée-sur-Google-Apps-Script--chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--85ef3-tée-sur-Google-Apps-Script--chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--85ef3-tée-sur-Google-Apps-Script--chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 16, "line": 34}}, {"workerIndex": 7, "parallelIndex": 0, "status": "passed", "duration": 11589, "errors": [], "stdout": [{"text": "❌ Système d'alertes temps réel non implémenté ou limité par Google Apps Script\n"}, {"text": "⚠️ Page responsive mais alertes temps réel non disponibles\n"}], "stderr": [], "retry": 1, "startTime": "2025-08-08T19:30:21.170Z", "annotations": [], "attachments": [{"name": "trace", "contentType": "application/zip", "path": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\test-results\\dashboard-epic3-Dashboard--85ef3-tée-sur-Google-Apps-Script--chromium-retry1\\trace.zip"}]}], "status": "flaky"}], "id": "130fd2ee41949c9c0fbc-c48caf63b46a9056979f", "file": "dashboard-epic3.spec.ts", "line": 176, "column": 7}, {"title": "7. Application des filtres en temps réel", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "passed", "duration": 4454, "errors": [], "stdout": [{"text": "⚠️ Aucun élément de filtrage trouvé - fonctionnalité non implémentée\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-08T19:30:33.287Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "130fd2ee41949c9c0fbc-efedc96c015d0777e30c", "file": "dashboard-epic3.spec.ts", "line": 212, "column": 7}, {"title": "8. Validation des permissions d'accès (limitée sur Google Apps Script)", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "passed", "duration": 3718, "errors": [], "stdout": [{"text": "⚠️ Éléments d'authentification non trouvés\n"}, {"text": "✅ Accès au dashboard autorisé\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-08T19:30:37.751Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "130fd2ee41949c9c0fbc-fee5d72a9dee2804505e", "file": "dashboard-epic3.spec.ts", "line": 242, "column": 7}, {"title": "9. Affichage des métriques de performance", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "passed", "duration": 20338, "errors": [], "stdout": [{"text": "❌ Métriques de performance cachées ou non implémentées\n"}, {"text": "⚠️ Élément métriques non implémenté\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-08T19:30:41.482Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "130fd2ee41949c9c0fbc-56eaec687a0f0ac7bc40", "file": "dashboard-epic3.spec.ts", "line": 271, "column": 7}, {"title": "10. Test de robustesse et responsivité (Google Apps Script)", "ok": true, "tags": [], "tests": [{"timeout": 60000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 0, "status": "passed", "duration": 8897, "errors": [], "stdout": [{"text": "Rechargement 1/3...\n"}, {"text": "Rechargement 2/3...\n"}, {"text": "Rechargement 3/3...\n"}, {"text": "✅ Dashboard robuste et responsive sur Google Apps Script\n"}], "stderr": [], "retry": 0, "startTime": "2025-08-08T19:31:01.830Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "130fd2ee41949c9c0fbc-4c76bc02f5830c7ef6bd", "file": "dashboard-epic3.spec.ts", "line": 298, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-08-08T19:26:43.973Z", "duration": 266927.23000000004, "expected": 6, "skipped": 0, "unexpected": 3, "flaky": 1}}