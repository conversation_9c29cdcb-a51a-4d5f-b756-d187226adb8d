#!/usr/bin/env node

/**
 * Script pour exécuter les tests du dashboard SIGMA
 * Utilise Jest pour les tests unitaires et d'intégration
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🧪 Exécution des tests du dashboard SIGMA...\n');

// Configuration Jest pour le dashboard
const jestConfig = {
  testEnvironment: 'jsdom',
  testMatch: [
    '**/src/js/dashboard/__tests__/**/*.test.js'
  ],
  setupFilesAfterEnv: [
    '<rootDir>/scripts/test-setup.js'
  ],
  collectCoverageFrom: [
    'src/js/dashboard/**/*.js',
    '!src/js/dashboard/__tests__/**',
    '!src/js/dashboard/**/*.test.js'
  ],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  verbose: true
};

// C<PERSON>er le fichier de configuration Jest temporaire
const configPath = path.join(__dirname, '..', 'jest.dashboard.config.js');
const configContent = `module.exports = ${JSON.stringify(jestConfig, null, 2)};`;

try {
  // Écrire la configuration
  fs.writeFileSync(configPath, configContent);
  
  // Exécuter Jest
  console.log('📊 Exécution des tests unitaires...');
  execSync(`npx jest --config=${configPath}`, {
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });
  
  console.log('\n✅ Tous les tests sont passés avec succès !');
  
} catch (error) {
  console.error('\n❌ Échec des tests:', error.message);
  process.exit(1);
  
} finally {
  // Nettoyer le fichier de configuration temporaire
  if (fs.existsSync(configPath)) {
    fs.unlinkSync(configPath);
  }
}
