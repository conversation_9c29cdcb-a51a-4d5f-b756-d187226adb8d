<testsuites id="" name="" tests="10" failures="3" skipped="0" errors="0" time="266.92723000000007">
<testsuite name="dashboard-epic3.spec.ts" timestamp="2025-08-08T19:26:44.041Z" hostname="chromium" tests="10" failures="3" skipped="0" time="248.805" errors="0">
<testcase name="Dashboard SIGMA - Épique E-3 (Google Apps Script) › 1. Navigation vers le dashboard" classname="dashboard-epic3.spec.ts" time="43.734">
<failure message="dashboard-epic3.spec.ts:40:7 1. Navigation vers le dashboard" type="FAILURE">
<![CDATA[  [chromium] › dashboard-epic3.spec.ts:40:7 › Dashboard SIGMA - Épique E-3 (Google Apps Script) › 1. Navigation vers le dashboard 

    Error: Timed out 15000ms waiting for expect(page).toHaveTitle(expected)

    Expected pattern: /SIGMA/i
    Received string:  "Apps Script  |  Google for Developers"
    Call log:
      - Expect "toHaveTitle" with timeout 15000ms
        18 × unexpected value "Apps Script  |  Google for Developers"


      42 |
      43 |     // Vérifier que la page se charge correctement
    > 44 |     await expect(page).toHaveTitle(/SIGMA/i, { timeout: 15000 });
         |                        ^
      45 |
      46 |     // Vérifier le titre principal
      47 |     const mainTitle = page.locator('h1');
        at C:\Users\<USER>\Documents\SIGMA-AGENT\tests\e2e\dashboard-epic3.spec.ts:44:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium\error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: Timed out 15000ms waiting for expect(page).toHaveTitle(expected)

    Expected pattern: /SIGMA/i
    Received string:  "Apps Script  |  Google for Developers"
    Call log:
      - Expect "toHaveTitle" with timeout 15000ms
        18 × unexpected value "Apps Script  |  Google for Developers"


      42 |
      43 |     // Vérifier que la page se charge correctement
    > 44 |     await expect(page).toHaveTitle(/SIGMA/i, { timeout: 15000 });
         |                        ^
      45 |
      46 |     // Vérifier le titre principal
      47 |     const mainTitle = page.locator('h1');
        at C:\Users\<USER>\Documents\SIGMA-AGENT\tests\e2e\dashboard-epic3.spec.ts:44:24

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace ..\..\test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium\video.webm]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium\error-context.md]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium-retry1\video.webm]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium-retry1\error-context.md]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--49e82-avigation-vers-le-dashboard-chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard SIGMA - Épique E-3 (Google Apps Script) › 2. Affichage des 7 tableaux du dashboard" classname="dashboard-epic3.spec.ts" time="45.753">
<failure message="dashboard-epic3.spec.ts:67:7 2. Affichage des 7 tableaux du dashboard" type="FAILURE">
<![CDATA[  [chromium] › dashboard-epic3.spec.ts:67:7 › Dashboard SIGMA - Épique E-3 (Google Apps Script) › 2. Affichage des 7 tableaux du dashboard 

    Error: Timed out 15000ms waiting for expect(locator).toHaveCount(expected)

    Locator: locator('.dashboard-card')
    Expected: 7
    Received: 0
    Call log:
      - Expect "toHaveCount" with timeout 15000ms
      - waiting for locator('.dashboard-card')
        18 × locator resolved to 0 elements
           - unexpected value "0"


      73 |
      74 |     try {
    > 75 |       await expect(dashboardCards).toHaveCount(7, { timeout: 15000 });
         |                                    ^
      76 |       console.log('✅ Les 7 tableaux du dashboard sont présents');
      77 |     } catch (error) {
      78 |       console.log('❌ Les 7 tableaux du dashboard ne sont pas tous présents');
        at C:\Users\<USER>\Documents\SIGMA-AGENT\tests\e2e\dashboard-epic3.spec.ts:75:36

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium\error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: Timed out 15000ms waiting for expect(locator).toHaveCount(expected)

    Locator: locator('.dashboard-card')
    Expected: 7
    Received: 0
    Call log:
      - Expect "toHaveCount" with timeout 15000ms
      - waiting for locator('.dashboard-card')
        18 × locator resolved to 0 elements
           - unexpected value "0"


      73 |
      74 |     try {
    > 75 |       await expect(dashboardCards).toHaveCount(7, { timeout: 15000 });
         |                                    ^
      76 |       console.log('✅ Les 7 tableaux du dashboard sont présents');
      77 |     } catch (error) {
      78 |       console.log('❌ Les 7 tableaux du dashboard ne sont pas tous présents');
        at C:\Users\<USER>\Documents\SIGMA-AGENT\tests\e2e\dashboard-epic3.spec.ts:75:36

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace ..\..\test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[❌ Les 7 tableaux du dashboard ne sont pas tous présents
Nombre de cartes trouvées: 0/7

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium\video.webm]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium\error-context.md]]
❌ Les 7 tableaux du dashboard ne sont pas tous présents
Nombre de cartes trouvées: 0/7

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium-retry1\video.webm]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium-retry1\error-context.md]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--83dae-des-7-tableaux-du-dashboard-chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard SIGMA - Épique E-3 (Google Apps Script) › 3. Chargement des données en moins de 5 secondes (Google Apps Script)" classname="dashboard-epic3.spec.ts" time="10.22">
<system-out>
<![CDATA[❌ Chargement en 5874ms ou métriques non visibles
⚠️ Page chargée mais métriques non visibles
]]>
</system-out>
</testcase>
<testcase name="Dashboard SIGMA - Épique E-3 (Google Apps Script) › 4. Affichage des alertes stock critiques" classname="dashboard-epic3.spec.ts" time="44.796">
<failure message="dashboard-epic3.spec.ts:122:7 4. Affichage des alertes stock critiques" type="FAILURE">
<![CDATA[  [chromium] › dashboard-epic3.spec.ts:122:7 › Dashboard SIGMA - Épique E-3 (Google Apps Script) › 4. Affichage des alertes stock critiques 

    Error: Timed out 15000ms waiting for expect(locator).toBeVisible()

    Locator: locator('#stock-alerts')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 15000ms
      - waiting for locator('#stock-alerts')


      128 |
      129 |     try {
    > 130 |       await expect(stockAlertsSection).toBeVisible({ timeout: 15000 });
          |                                        ^
      131 |
      132 |       // Vérifier le contenu des alertes
      133 |       const alertContent = page.locator('#stock-alerts .card-content');
        at C:\Users\<USER>\Documents\SIGMA-AGENT\tests\e2e\dashboard-epic3.spec.ts:130:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium\error-context.md

    Retry #1 ───────────────────────────────────────────────────────────────────────────────────────

    Error: Timed out 15000ms waiting for expect(locator).toBeVisible()

    Locator: locator('#stock-alerts')
    Expected: visible
    Received: <element(s) not found>
    Call log:
      - Expect "toBeVisible" with timeout 15000ms
      - waiting for locator('#stock-alerts')


      128 |
      129 |     try {
    > 130 |       await expect(stockAlertsSection).toBeVisible({ timeout: 15000 });
          |                                        ^
      131 |
      132 |       // Vérifier le contenu des alertes
      133 |       const alertContent = page.locator('#stock-alerts .card-content');
        at C:\Users\<USER>\Documents\SIGMA-AGENT\tests\e2e\dashboard-epic3.spec.ts:130:40

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium-retry1\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium-retry1\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\..\test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium-retry1\error-context.md

    attachment #4: trace (application/zip) ─────────────────────────────────────────────────────────
    ..\..\test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium-retry1\trace.zip
    Usage:

        npx playwright show-trace ..\..\test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium-retry1\trace.zip

    ────────────────────────────────────────────────────────────────────────────────────────────────
]]>
</failure>
<system-out>
<![CDATA[❌ Section alertes stock non trouvée

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium\video.webm]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium\error-context.md]]
❌ Section alertes stock non trouvée

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium-retry1\test-failed-1.png]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium-retry1\video.webm]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium-retry1\error-context.md]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--23d0b-des-alertes-stock-critiques-chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard SIGMA - Épique E-3 (Google Apps Script) › 5. Gestion de l&apos;affichage/masquage des alertes" classname="dashboard-epic3.spec.ts" time="20.626">
<system-out>
<![CDATA[❌ Section alertes non accessible pour interaction
⚠️ Test ignoré - fonctionnalité en cours d'implémentation
]]>
</system-out>
</testcase>
<testcase name="Dashboard SIGMA - Épique E-3 (Google Apps Script) › 6. Simulation d&apos;alerte en temps réel (limitée sur Google Apps Script)" classname="dashboard-epic3.spec.ts" time="46.269">
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--85ef3-tée-sur-Google-Apps-Script--chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--85ef3-tée-sur-Google-Apps-Script--chromium\video.webm]]

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--85ef3-tée-sur-Google-Apps-Script--chromium\error-context.md]]
❌ Système d'alertes temps réel non implémenté ou limité par Google Apps Script
⚠️ Page responsive mais alertes temps réel non disponibles

[[ATTACHMENT|test-results\dashboard-epic3-Dashboard--85ef3-tée-sur-Google-Apps-Script--chromium-retry1\trace.zip]]
]]>
</system-out>
</testcase>
<testcase name="Dashboard SIGMA - Épique E-3 (Google Apps Script) › 7. Application des filtres en temps réel" classname="dashboard-epic3.spec.ts" time="4.454">
<system-out>
<![CDATA[⚠️ Aucun élément de filtrage trouvé - fonctionnalité non implémentée
]]>
</system-out>
</testcase>
<testcase name="Dashboard SIGMA - Épique E-3 (Google Apps Script) › 8. Validation des permissions d&apos;accès (limitée sur Google Apps Script)" classname="dashboard-epic3.spec.ts" time="3.718">
<system-out>
<![CDATA[⚠️ Éléments d'authentification non trouvés
✅ Accès au dashboard autorisé
]]>
</system-out>
</testcase>
<testcase name="Dashboard SIGMA - Épique E-3 (Google Apps Script) › 9. Affichage des métriques de performance" classname="dashboard-epic3.spec.ts" time="20.338">
<system-out>
<![CDATA[❌ Métriques de performance cachées ou non implémentées
⚠️ Élément métriques non implémenté
]]>
</system-out>
</testcase>
<testcase name="Dashboard SIGMA - Épique E-3 (Google Apps Script) › 10. Test de robustesse et responsivité (Google Apps Script)" classname="dashboard-epic3.spec.ts" time="8.897">
<system-out>
<![CDATA[Rechargement 1/3...
Rechargement 2/3...
Rechargement 3/3...
✅ Dashboard robuste et responsive sur Google Apps Script
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>