/**
 * Configuration globale pour les tests du dashboard
 * Setup des mocks et utilitaires de test
 */

// Mock des APIs du navigateur
global.console = {
  ...console,
  // Supprimer les logs pendant les tests sauf erreurs
  log: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: console.error
};

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};
global.localStorage = localStorageMock;

// Mock sessionStorage
global.sessionStorage = localStorageMock;

// Mock window.location
delete window.location;
window.location = {
  href: 'http://localhost:3000',
  origin: 'http://localhost:3000',
  reload: jest.fn()
};

// Mock des timers pour les tests
jest.useFakeTimers();

// Charger les classes du dashboard
const path = require('path');
const fs = require('fs');

// Fonction helper pour charger un fichier JS dans le contexte global
function loadScript(filePath) {
  const fullPath = path.join(__dirname, '..', filePath);
  if (fs.existsSync(fullPath)) {
    const content = fs.readFileSync(fullPath, 'utf8');
    // Évaluer le script dans le contexte global
    eval(content);
  }
}

// Charger les classes nécessaires
beforeAll(() => {
  // Charger PaginationManager
  loadScript('src/js/dashboard/PaginationManager.js');
  
  // Charger DashboardUI
  loadScript('src/js/dashboard/DashboardUI.js');
  
  // Charger DashboardManager
  loadScript('src/js/dashboard/DashboardManager.js');
});

// Nettoyage après chaque test
afterEach(() => {
  jest.clearAllMocks();
  jest.clearAllTimers();
  
  // Nettoyer les variables globales
  delete global.dashboardManager;
  delete global.dashboardUI;
  delete global.paginationManager;
  
  // Reset DOM
  document.body.innerHTML = '';
});

// Utilitaires de test
global.testUtils = {
  // Créer un mock snapshot Firestore
  createMockSnapshot: (docs) => ({
    docs: docs.map(doc => ({
      id: doc.id,
      data: () => doc.data
    }))
  }),
  
  // Créer un mock user Firebase
  createMockUser: (role = 'regisseur') => ({
    uid: 'test-user-id',
    email: '<EMAIL>',
    getIdTokenResult: jest.fn().mockResolvedValue({
      claims: { role }
    })
  }),
  
  // Attendre que les timers se terminent
  flushTimers: () => {
    jest.runAllTimers();
    return new Promise(resolve => setImmediate(resolve));
  },
  
  // Créer un élément DOM mock
  createMockElement: (id) => {
    const element = {
      id,
      innerHTML: '',
      className: '',
      style: {},
      addEventListener: jest.fn(),
      removeEventListener: jest.fn(),
      querySelector: jest.fn(),
      querySelectorAll: jest.fn(() => []),
      appendChild: jest.fn(),
      removeChild: jest.fn(),
      classList: {
        add: jest.fn(),
        remove: jest.fn(),
        contains: jest.fn(),
        toggle: jest.fn()
      }
    };
    
    // Mock getElementById pour retourner cet élément
    const originalGetElementById = document.getElementById;
    document.getElementById = jest.fn((elementId) => {
      if (elementId === id) return element;
      return originalGetElementById.call(document, elementId);
    });
    
    return element;
  }
};

// Configuration des matchers Jest personnalisés
expect.extend({
  toHaveBeenCalledWithSnapshot(received, expectedDocs) {
    const calls = received.mock.calls;
    const lastCall = calls[calls.length - 1];
    
    if (!lastCall || !lastCall[0]) {
      return {
        message: () => 'Expected function to have been called with snapshot data',
        pass: false
      };
    }
    
    const actualData = lastCall[0];
    const pass = expectedDocs.every((expectedDoc, index) => {
      const actualDoc = actualData[index];
      return actualDoc && actualDoc.id === expectedDoc.id;
    });
    
    return {
      message: () => pass 
        ? 'Expected function not to have been called with matching snapshot'
        : 'Expected function to have been called with matching snapshot',
      pass
    };
  }
});

console.log('✅ Configuration des tests chargée');
