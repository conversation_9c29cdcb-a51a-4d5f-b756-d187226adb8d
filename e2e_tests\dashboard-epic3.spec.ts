import { test, expect } from '@playwright/test';

/**
 * Tests E2E pour l'Épique E-3 : Dashboard & Alerting
 * Version adaptée pour l'environnement local
 *
 * Ces tests valident les fonctionnalités clés du dashboard SIGMA :
 * - Navigation et structure
 * - Affichage des 7 tableaux
 * - Alertes et notifications
 * - Performance de chargement
 */

test.describe('Dashboard SIGMA - Épique E-3', () => {

  test.beforeEach(async ({ page }) => {
    // Navigation vers la page dashboard locale
    await page.goto('/html/dashboard.html');
    
    // Attendre que la page soit complètement chargée
    await page.waitForLoadState('domcontentloaded');
  });

  test('1. Navigation vers le dashboard', async ({ page }) => {
    // Vérifier que la page se charge correctement
    await expect(page).toHaveTitle(/SIGMA/i, { timeout: 15000 });

    // Vérifier le titre principal
    const mainTitle = page.locator('h1');
    await expect(mainTitle).toBeVisible({ timeout: 15000 });
    await expect(mainTitle).toContainText('Dashboard SIGMA');

    // Vérifier la structure de base
    await expect(page.locator('.dashboard-header')).toBeVisible({ timeout: 10000 });

    // Vérifier la présence de la grille dashboard
    const dashboardGrid = page.locator('.dashboard-grid');
    await expect(dashboardGrid).toBeVisible({ timeout: 5000 });
    
    console.log('✅ Navigation vers le dashboard réussie');
  });

  test('2. Affichage des 7 tableaux du dashboard', async ({ page }) => {
    // Attendre le chargement complet de la page
    await page.waitForLoadState('domcontentloaded');

    // Vérifier la présence des 7 cartes principales
    const dashboardCards = page.locator('.dashboard-card');
    
    await expect(dashboardCards).toHaveCount(7, { timeout: 15000 });
    console.log('✅ Les 7 tableaux du dashboard sont présents');

    // Vérifier que chaque carte a un contenu
    for (let i = 0; i < 7; i++) {
      const card = dashboardCards.nth(i);
      const cardContent = card.locator('.card-content');
      await expect(cardContent).toBeVisible({ timeout: 5000 });
      
      const cardTitle = card.locator('h4');
      await expect(cardTitle).toBeVisible({ timeout: 5000 });
    }
    
    console.log('✅ Toutes les cartes ont un contenu visible');
  });

  test('3. Vérification des IDs spécifiques des cartes', async ({ page }) => {
    await page.waitForLoadState('domcontentloaded');

    // Vérifier la présence de chaque carte avec son ID spécifique
    const expectedCards = [
      '#stock-alerts',
      '#missing-material', 
      '#overdue-emprunts',
      '#upcoming-emprunts',
      '#non-op-modules',
      '#non-op-material',
      '#pending-emprunts'
    ];

    for (const cardId of expectedCards) {
      const card = page.locator(cardId);
      await expect(card).toBeVisible({ timeout: 5000 });
      
      // Vérifier que la carte a la classe dashboard-card
      await expect(card).toHaveClass(/dashboard-card/);
      
      console.log(`✅ Carte ${cardId} trouvée et visible`);
    }
  });

  test('4. Affichage des alertes stock critiques', async ({ page }) => {
    await page.waitForLoadState('domcontentloaded');

    // Vérifier la section des alertes stock
    const stockAlertsSection = page.locator('#stock-alerts');
    await expect(stockAlertsSection).toBeVisible({ timeout: 15000 });

    // Vérifier le contenu des alertes
    const alertContent = page.locator('#stock-alerts .card-content');
    await expect(alertContent).toBeVisible({ timeout: 10000 });

    // Vérifier le titre de la section
    const alertTitle = page.locator('#stock-alerts h4');
    await expect(alertTitle).toBeVisible({ timeout: 5000 });
    await expect(alertTitle).toContainText('Alertes Stock');

    console.log('✅ Section alertes stock visible et correctement structurée');
  });

  test('5. Chargement des métriques de performance', async ({ page }) => {
    await page.waitForLoadState('domcontentloaded');

    const performanceMetrics = page.locator('#performance-metrics');
    
    // Vérifier que l'élément existe (même s'il peut être vide)
    await expect(performanceMetrics).toBeAttached({ timeout: 5000 });
    
    console.log('✅ Élément métriques de performance présent');
  });

  test('6. Structure générale du dashboard', async ({ page }) => {
    await page.waitForLoadState('domcontentloaded');

    // Vérifier la structure générale
    const header = page.locator('.dashboard-header');
    await expect(header).toBeVisible({ timeout: 5000 });

    const container = page.locator('.dashboard-container');
    await expect(container).toBeVisible({ timeout: 5000 });

    const grid = page.locator('.dashboard-grid');
    await expect(grid).toBeVisible({ timeout: 5000 });

    // Vérifier les éléments d'interface utilisateur
    const userInfo = page.locator('#user-info');
    await expect(userInfo).toBeVisible({ timeout: 5000 });

    const refreshIndicator = page.locator('#refresh-indicator');
    await expect(refreshIndicator).toBeVisible({ timeout: 5000 });

    console.log('✅ Structure générale du dashboard correcte');
  });

  test('7. Vérification des classes CSS des cartes', async ({ page }) => {
    await page.waitForLoadState('domcontentloaded');

    // Vérifier les classes spécifiques des cartes
    const criticalCards = page.locator('.dashboard-card.critical');
    await expect(criticalCards).toHaveCount(2); // stock-alerts et overdue-emprunts

    const warningCards = page.locator('.dashboard-card.warning');
    await expect(warningCards).toHaveCount(2); // non-op-modules et non-op-material

    const infoCards = page.locator('.dashboard-card.info');
    await expect(infoCards).toHaveCount(1); // upcoming-emprunts

    console.log('✅ Classes CSS des cartes correctement appliquées');
  });
});
