"use strict";
/**
 * Gestion des utilisateurs côté serveur
 * Fonctions pour lister, récupérer et mettre à jour les profils utilisateurs
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateUserProfile = exports.listUsers = exports.getUserRole = void 0;
exports.hasPermission = hasPermission;
const https_1 = require("firebase-functions/v2/https");
const auth_1 = require("firebase-admin/auth");
const firestore_1 = require("firebase-admin/firestore");
const logger = __importStar(require("firebase-functions/logger"));
const zod_1 = require("zod");
// Schémas de validation
const GetUserRoleSchema = zod_1.z.object({
    userId: zod_1.z.string().min(1, 'ID utilisateur requis')
});
const UpdateUserProfileSchema = zod_1.z.object({
    userId: zod_1.z.string().min(1, 'ID utilisateur requis'),
    displayName: zod_1.z.string().optional(),
    preferences: zod_1.z.object({
        language: zod_1.z.enum(['fr', 'en']).optional(),
        theme: zod_1.z.enum(['light', 'dark']).optional(),
        notifications: zod_1.z.object({
            email: zod_1.z.boolean().optional(),
            browser: zod_1.z.boolean().optional(),
            emprunts: zod_1.z.boolean().optional(),
            stocks: zod_1.z.boolean().optional(),
            livraisons: zod_1.z.boolean().optional()
        }).optional(),
        dashboard: zod_1.z.object({
            showWelcome: zod_1.z.boolean().optional(),
            defaultView: zod_1.z.enum(['overview', 'emprunts', 'stocks']).optional()
        }).optional()
    }).optional()
});
const ListUsersSchema = zod_1.z.object({
    limit: zod_1.z.number().min(1).max(100).optional().default(20),
    startAfter: zod_1.z.string().optional(), // Pour la pagination
    role: zod_1.z.enum(['admin', 'regisseur', 'utilisateur']).optional(),
    searchTerm: zod_1.z.string().optional() // Pour rechercher par email/nom
});
/**
 * Récupérer le rôle et les informations d'un utilisateur
 */
exports.getUserRole = (0, https_1.onCall)({
    region: 'europe-west1',
    maxInstances: 10,
    memory: '256MiB',
    timeoutSeconds: 30
}, async (request) => {
    var _a, _b;
    try {
        // Vérification de l'authentification
        if (!request.auth) {
            throw new https_1.HttpsError('unauthenticated', 'Authentification requise');
        }
        const callerId = request.auth.uid;
        const callerRole = request.auth.token.role;
        // Validation des données
        const { userId } = GetUserRoleSchema.parse(request.data);
        // Vérification des permissions
        // Un utilisateur peut voir ses propres infos, les admins et régisseurs peuvent voir tous les utilisateurs
        if (callerId !== userId && !['admin', 'regisseur'].includes(callerRole)) {
            throw new https_1.HttpsError('permission-denied', 'Permissions insuffisantes');
        }
        // Récupération des informations utilisateur
        const auth = (0, auth_1.getAuth)();
        const db = (0, firestore_1.getFirestore)();
        const [authUser, firestoreUser] = await Promise.all([
            auth.getUser(userId).catch(() => null),
            db.collection('users').doc(userId).get()
        ]);
        if (!authUser) {
            throw new https_1.HttpsError('not-found', 'Utilisateur introuvable');
        }
        const customClaims = authUser.customClaims || {};
        const firestoreData = firestoreUser.exists ? firestoreUser.data() : null;
        return {
            uid: authUser.uid,
            email: authUser.email,
            displayName: authUser.displayName,
            photoURL: authUser.photoURL,
            emailVerified: authUser.emailVerified,
            role: customClaims.role || 'utilisateur',
            roleAssignedAt: customClaims.roleAssignedAt,
            roleAssignedBy: customClaims.roleAssignedBy,
            createdAt: authUser.metadata.creationTime,
            lastSignInTime: authUser.metadata.lastSignInTime,
            isActive: (_a = firestoreData === null || firestoreData === void 0 ? void 0 : firestoreData.isActive) !== null && _a !== void 0 ? _a : true,
            preferences: (firestoreData === null || firestoreData === void 0 ? void 0 : firestoreData.preferences) || {},
            stats: (firestoreData === null || firestoreData === void 0 ? void 0 : firestoreData.stats) || {}
        };
    }
    catch (error) {
        if (error instanceof https_1.HttpsError)
            throw error;
        logger.error('❌ Erreur dans getUserRole', {
            error: error instanceof Error ? error.message : String(error),
            callerId: (_b = request.auth) === null || _b === void 0 ? void 0 : _b.uid
        });
        throw new https_1.HttpsError('internal', 'Erreur interne du serveur');
    }
});
/**
 * Lister les utilisateurs avec pagination et filtres
 */
exports.listUsers = (0, https_1.onCall)({
    region: 'europe-west1',
    maxInstances: 5,
    memory: '512MiB',
    timeoutSeconds: 60
}, async (request) => {
    var _a, _b, _c, _d, _e, _f;
    try {
        // Vérification de l'authentification
        if (!request.auth) {
            throw new https_1.HttpsError('unauthenticated', 'Authentification requise');
        }
        const callerRole = request.auth.token.role;
        // Seuls les admins et régisseurs peuvent lister les utilisateurs
        if (!['admin', 'regisseur'].includes(callerRole)) {
            throw new https_1.HttpsError('permission-denied', 'Permissions insuffisantes');
        }
        // Validation des données
        const { limit, startAfter, role, searchTerm } = ListUsersSchema.parse(request.data);
        const db = (0, firestore_1.getFirestore)();
        let query = db.collection('users').orderBy('createdAt', 'desc');
        // Filtrage par rôle
        if (role) {
            query = query.where('role', '==', role);
        }
        // Pagination
        if (startAfter) {
            const startAfterDoc = await db.collection('users').doc(startAfter).get();
            if (startAfterDoc.exists) {
                query = query.startAfter(startAfterDoc);
            }
        }
        // Limitation
        query = query.limit(limit);
        const snapshot = await query.get();
        const users = [];
        for (const doc of snapshot.docs) {
            const userData = doc.data();
            // Filtrage par terme de recherche (côté serveur pour plus de sécurité)
            if (searchTerm) {
                const searchLower = searchTerm.toLowerCase();
                const emailMatch = (_a = userData.email) === null || _a === void 0 ? void 0 : _a.toLowerCase().includes(searchLower);
                const nameMatch = (_b = userData.displayName) === null || _b === void 0 ? void 0 : _b.toLowerCase().includes(searchLower);
                if (!emailMatch && !nameMatch) {
                    continue;
                }
            }
            users.push({
                uid: doc.id,
                email: userData.email,
                displayName: userData.displayName,
                role: userData.role,
                isActive: userData.isActive,
                createdAt: userData.createdAt,
                lastActivityAt: (_c = userData.stats) === null || _c === void 0 ? void 0 : _c.lastActivityAt,
                empruntsCount: ((_d = userData.stats) === null || _d === void 0 ? void 0 : _d.empruntsCount) || 0
            });
        }
        return {
            users,
            hasMore: snapshot.size === limit,
            lastUserId: (_e = snapshot.docs[snapshot.docs.length - 1]) === null || _e === void 0 ? void 0 : _e.id
        };
    }
    catch (error) {
        if (error instanceof https_1.HttpsError)
            throw error;
        logger.error('❌ Erreur dans listUsers', {
            error: error instanceof Error ? error.message : String(error),
            callerId: (_f = request.auth) === null || _f === void 0 ? void 0 : _f.uid
        });
        throw new https_1.HttpsError('internal', 'Erreur interne du serveur');
    }
});
/**
 * Mettre à jour le profil utilisateur
 */
exports.updateUserProfile = (0, https_1.onCall)({
    region: 'europe-west1',
    maxInstances: 10,
    memory: '256MiB',
    timeoutSeconds: 30
}, async (request) => {
    var _a;
    try {
        // Vérification de l'authentification
        if (!request.auth) {
            throw new https_1.HttpsError('unauthenticated', 'Authentification requise');
        }
        const callerId = request.auth.uid;
        const callerRole = request.auth.token.role;
        // Validation des données
        const { userId, displayName, preferences } = UpdateUserProfileSchema.parse(request.data);
        // Vérification des permissions
        // Un utilisateur peut modifier son propre profil, les admins peuvent modifier tous les profils
        if (callerId !== userId && callerRole !== 'admin') {
            throw new https_1.HttpsError('permission-denied', 'Permissions insuffisantes');
        }
        const auth = (0, auth_1.getAuth)();
        const db = (0, firestore_1.getFirestore)();
        // Transaction pour cohérence des données
        const result = await db.runTransaction(async (transaction) => {
            var _a, _b, _c;
            // Vérifier que l'utilisateur existe
            const userRef = db.collection('users').doc(userId);
            const userDoc = await transaction.get(userRef);
            if (!userDoc.exists) {
                throw new https_1.HttpsError('not-found', 'Utilisateur introuvable');
            }
            const updates = {
                updatedAt: new Date(),
                updatedBy: callerId
            };
            // Mise à jour du displayName dans Auth et Firestore
            if (displayName !== undefined) {
                await auth.updateUser(userId, { displayName });
                updates.displayName = displayName;
            }
            // Mise à jour des préférences
            if (preferences) {
                const currentPrefs = ((_a = userDoc.data()) === null || _a === void 0 ? void 0 : _a.preferences) || {};
                updates.preferences = Object.assign(Object.assign(Object.assign({}, currentPrefs), preferences), { 
                    // Merge des objets imbriqués
                    notifications: Object.assign(Object.assign({}, currentPrefs.notifications), preferences.notifications), dashboard: Object.assign(Object.assign({}, currentPrefs.dashboard), preferences.dashboard) });
            }
            // Mise à jour des statistiques d'activité
            updates['stats.lastActivityAt'] = new Date();
            transaction.update(userRef, updates);
            return {
                uid: userId,
                displayName: displayName || ((_b = userDoc.data()) === null || _b === void 0 ? void 0 : _b.displayName),
                preferences: updates.preferences || ((_c = userDoc.data()) === null || _c === void 0 ? void 0 : _c.preferences)
            };
        });
        logger.info('✅ Profil utilisateur mis à jour', {
            userId,
            updatedBy: callerId,
            fields: Object.keys(request.data).filter(k => k !== 'userId')
        });
        return {
            success: true,
            message: 'Profil mis à jour avec succès',
            user: result
        };
    }
    catch (error) {
        if (error instanceof https_1.HttpsError)
            throw error;
        logger.error('❌ Erreur dans updateUserProfile', {
            error: error instanceof Error ? error.message : String(error),
            callerId: (_a = request.auth) === null || _a === void 0 ? void 0 : _a.uid
        });
        throw new https_1.HttpsError('internal', 'Erreur interne du serveur');
    }
});
/**
 * Fonction utilitaire pour valider les permissions
 */
function hasPermission(userRole, action, targetUserId, callerId) {
    switch (action) {
        case 'view_user':
            return userRole === 'admin' || userRole === 'regisseur' || (targetUserId === callerId);
        case 'edit_user':
            return userRole === 'admin' || (targetUserId === callerId);
        case 'list_users':
            return userRole === 'admin' || userRole === 'regisseur';
        case 'assign_role':
            return userRole === 'admin';
        default:
            return false;
    }
}
//# sourceMappingURL=userManagement.js.map