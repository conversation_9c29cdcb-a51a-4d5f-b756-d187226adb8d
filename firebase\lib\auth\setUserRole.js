"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.setUserRole = void 0;
// functions/src/auth/setUserRole.ts
const https_1 = require("firebase-functions/v2/https");
const firebase_functions_1 = require("firebase-functions");
const auth_1 = require("firebase-admin/auth");
const firestore_1 = require("firebase-admin/firestore");
const ALLOWED_ROLES = new Set(['utilisateur', 'regisseur', 'admin']);
const HTTPS_ERROR_CODES = new Set([
    'cancelled', 'unknown', 'invalid-argument', 'deadline-exceeded', 'not-found', 'already-exists',
    'permission-denied', 'resource-exhausted', 'failed-precondition', 'aborted', 'out-of-range',
    'unimplemented', 'internal', 'unavailable', 'data-loss', 'unauthenticated'
]);
exports.setUserRole = (0, https_1.onCall)({ region: 'europe-west1' }, async (request) => {
    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k, _l;
    try {
        // 1) Auth requise
        if (!request.auth) {
            throw new https_1.HttpsError('unauthenticated', 'Authentification requise.');
        }
        // 2) Vérif du rôle appelant (doit être admin)
        const requesterUid = request.auth.uid;
        const requesterRole = String((_b = (_a = request.auth.token) === null || _a === void 0 ? void 0 : _a.role) !== null && _b !== void 0 ? _b : 'utilisateur');
        if (requesterRole !== 'admin') {
            firebase_functions_1.logger.warn('Tentative d’accès non autorisé', {
                uid: requesterUid,
                userRole: requesterRole,
                requiredRole: 'admin',
            });
            throw new https_1.HttpsError('permission-denied', 'Accès refusé. Rôle requis: admin');
        }
        // 3) Validation input
        const userId = String((_f = (_d = (_c = request.data) === null || _c === void 0 ? void 0 : _c.userId) !== null && _d !== void 0 ? _d : (_e = request.data) === null || _e === void 0 ? void 0 : _e.uid) !== null && _f !== void 0 ? _f : '').trim();
        const role = String((_h = (_g = request.data) === null || _g === void 0 ? void 0 : _g.role) !== null && _h !== void 0 ? _h : '').trim();
        if (!userId)
            throw new https_1.HttpsError('invalid-argument', "Paramètre 'userId' requis.");
        if (!role || !ALLOWED_ROLES.has(role)) {
            throw new https_1.HttpsError('invalid-argument', "Paramètre 'role' invalide.");
        }
        // 4) Opérations Auth + Firestore
        const auth = (0, auth_1.getAuth)();
        await auth.getUser(userId); // vérifie existence
        await auth.setCustomUserClaims(userId, { role });
        const db = (0, firestore_1.getFirestore)();
        await db.collection('users').doc(userId).set({ role }, { merge: true });
        // 5) Réponse OK
        return { success: true, newRole: role };
    }
    catch (err) {
        firebase_functions_1.logger.error('Erreur dans setUserRole', {
            code: (_j = err === null || err === void 0 ? void 0 : err.code) !== null && _j !== void 0 ? _j : (_k = err === null || err === void 0 ? void 0 : err.errorInfo) === null || _k === void 0 ? void 0 : _k.code,
            message: err === null || err === void 0 ? void 0 : err.message,
            stack: err === null || err === void 0 ? void 0 : err.stack,
        });
        // Si c'est déjà un HttpsError (code connu), ne pas le remapper en "internal"
        if (typeof (err === null || err === void 0 ? void 0 : err.code) === 'string' && HTTPS_ERROR_CODES.has(err.code)) {
            throw err;
        }
        // Mapping d'erreurs Admin SDK utiles
        const adminCode = ((_l = err === null || err === void 0 ? void 0 : err.errorInfo) === null || _l === void 0 ? void 0 : _l.code) || (err === null || err === void 0 ? void 0 : err.code);
        if (adminCode === 'auth/user-not-found') {
            throw new https_1.HttpsError('not-found', 'Utilisateur introuvable.');
        }
        // Fallback générique
        throw new https_1.HttpsError('internal', 'Erreur interne du serveur');
    }
});
//# sourceMappingURL=setUserRole.js.map