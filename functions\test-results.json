{"config": {"configFile": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "playwright-report"}], ["json", {"outputFile": "test-results.json"}], ["junit", {"outputFile": "test-results.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/functions/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/functions/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/functions/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/functions/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/functions/test-results", "repeatEach": 1, "retries": 0, "metadata": {}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Documents/SIGMA-AGENT/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.54.2", "workers": 2, "webServer": null}, "suites": [], "errors": [{"message": "Error: Cannot find module '@playwright/test'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\transform\\transform.js\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\common\\configLoader.js\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\program.js\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\@playwright\\test\\cli.js", "stack": "Error: Cannot find module '@playwright/test'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\transform\\transform.js\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\common\\configLoader.js\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\program.js\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\@playwright\\test\\cli.js\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts:1:1)", "location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\dashboard-epic3.spec.ts", "column": 1, "line": 1}, "snippet": "\u001b[90m   at \u001b[39mdashboard-epic3.spec.ts:1\n\n\u001b[0m\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 1 |\u001b[39m \u001b[36mimport\u001b[39m { test\u001b[33m,\u001b[39m expect } \u001b[36mfrom\u001b[39m \u001b[32m'@playwright/test'\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m \u001b[90m/**\u001b[39m\n \u001b[90m 4 |\u001b[39m \u001b[90m * Tests E2E pour l'Épique E-3 : Dashboard & Alerting\u001b[39m\u001b[0m"}, {"message": "Error: Cannot find module '@playwright/test'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\emprunt-creation.spec.ts\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\transform\\transform.js\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\common\\configLoader.js\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\program.js\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\@playwright\\test\\cli.js", "stack": "Error: Cannot find module '@playwright/test'\nRequire stack:\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\emprunt-creation.spec.ts\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\transform\\transform.js\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\common\\configLoader.js\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\playwright\\lib\\program.js\n- C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\functions\\node_modules\\@playwright\\test\\cli.js\n    at Object.<anonymous> (C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\emprunt-creation.spec.ts:1:1)", "location": {"file": "C:\\Users\\<USER>\\Documents\\SIGMA-AGENT\\tests\\e2e\\emprunt-creation.spec.ts", "column": 1, "line": 1}, "snippet": "\u001b[90m   at \u001b[39memprunt-creation.spec.ts:1\n\n\u001b[0m\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 1 |\u001b[39m \u001b[36mimport\u001b[39m { test\u001b[33m,\u001b[39m expect } \u001b[36mfrom\u001b[39m \u001b[32m'@playwright/test'\u001b[39m\u001b[33m;\u001b[39m\n \u001b[90m   |\u001b[39m \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 2 |\u001b[39m\n \u001b[90m 3 |\u001b[39m \u001b[90m/**\u001b[39m\n \u001b[90m 4 |\u001b[39m \u001b[90m * Tests E2E pour la création d'emprunt - Migration de Cypress vers Playwright\u001b[39m\u001b[0m"}, {"message": "Error: No tests found", "stack": "Error: No tests found"}], "stats": {"startTime": "2025-08-08T14:47:01.362Z", "duration": 865.8739999999998, "expected": 0, "skipped": 0, "unexpected": 0, "flaky": 0}}