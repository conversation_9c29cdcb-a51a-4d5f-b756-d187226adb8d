/**
 * Gestionnaire principal du dashboard SIGMA
 * Gestion des listeners temps-réel avec pagination et optimisations
 */

class DashboardManager {
  constructor() {
    this.listeners = new Map();
    this.lastDocs = new Map();
    this.debounceTimers = new Map();
    this.isInitialized = false;
    this.refreshIndicator = null;

    // Gestionnaires associés
    this.paginationManager = new PaginationManager();
    this.dashboardUI = new DashboardUI();
    
    // Configuration des tableaux
    this.tableConfigs = {
      stockAlerts: {
        name: 'Alertes Stock',
        icon: '🚨',
        containerId: 'stock-alerts-table',
        limit: 20,
        refreshInterval: 30000 // 30 secondes
      },
      missingMaterial: {
        name: '<PERSON><PERSON><PERSON>quant',
        icon: '❌',
        containerId: 'missing-material-table',
        limit: 20,
        refreshInterval: 60000 // 1 minute
      },
      overdueEmprunts: {
        name: 'Emprunts en Retard',
        icon: '⏰',
        containerId: 'overdue-emprunts-table',
        limit: 20,
        refreshInterval: 30000 // 30 secondes
      },
      upcomingEmprunts: {
        name: 'Prochains Emprunts',
        icon: '📅',
        containerId: 'upcoming-emprunts-table',
        limit: 20,
        refreshInterval: 300000 // 5 minutes
      },
      nonOpModules: {
        name: 'Modules Non Opérationnels',
        icon: '📌',
        containerId: 'non-op-modules-table',
        limit: 20,
        refreshInterval: 120000 // 2 minutes
      },
      nonOpMaterial: {
        name: 'Matériel Non Opérationnel',
        icon: '🔧',
        containerId: 'non-op-material-table',
        limit: 20,
        refreshInterval: 120000 // 2 minutes
      },
      pendingEmprunts: {
        name: 'Emprunts en Attente',
        icon: '📋',
        containerId: 'pending-emprunts-table',
        limit: 20,
        refreshInterval: 60000 // 1 minute
      }
    };
  }

  /**
   * Initialiser le dashboard
   */
  async initialize() {
    try {
      console.log('🚀 Initialisation du dashboard SIGMA...');
      
      // Vérifier l'authentification
      const user = firebase.auth().currentUser;
      if (!user) {
        throw new Error('Utilisateur non authentifié');
      }

      // Vérifier les permissions
      const idTokenResult = await user.getIdTokenResult();
      const userRole = idTokenResult.claims.role;
      
      if (!['regisseur', 'admin'].includes(userRole)) {
        throw new Error('Permissions insuffisantes pour accéder au dashboard');
      }

      // Initialiser l'indicateur de rafraîchissement
      this.initializeRefreshIndicator();

      // Initialiser la pagination pour tous les tableaux
      this.initializePagination();

      // Configurer les listeners temps-réel
      this.setupRealtimeListeners();

      // Charger les données initiales
      await this.loadInitialData();

      this.isInitialized = true;
      console.log('✅ Dashboard initialisé avec succès');

    } catch (error) {
      console.error('❌ Erreur lors de l\'initialisation du dashboard:', error);
      this.showError('Erreur d\'initialisation', error.message);
    }
  }

  /**
   * Configurer tous les listeners temps-réel
   */
  setupRealtimeListeners() {
    console.log('🔗 Configuration des listeners temps-réel...');

    // Listener pour alertes stock
    this.setupStockAlertsListener();
    
    // Listener pour matériel manquant
    this.setupMissingMaterialListener();
    
    // Listener pour emprunts en retard
    this.setupOverdueEmpruntsListener();
    
    // Listener pour prochains emprunts
    this.setupUpcomingEmpruntsListener();
    
    // Listener pour modules non opérationnels
    this.setupNonOpModulesListener();
    
    // Listener pour matériel non opérationnel
    this.setupNonOpMaterialListener();
    
    // Listener pour emprunts en attente
    this.setupPendingEmpruntsListener();

    console.log('✅ Tous les listeners configurés');
  }

  /**
   * Listener pour alertes stock (quantité <= seuil)
   * Optimisé pour récupérer seulement les stocks avec quantité faible
   */
  setupStockAlertsListener() {
    // Nettoyer l'ancien listener s'il existe
    if (this.listeners.has('stockAlerts')) {
      this.listeners.get('stockAlerts')();
    }

    // Requête optimisée : récupérer les stocks opérationnels avec quantité faible
    // Note: Firestore ne permet pas de comparer deux champs dans une requête,
    // donc nous devons filtrer côté client, mais limitons d'abord par quantité
    const query = firebase.firestore()
      .collection('stocks')
      .where('estOperationnel', '==', true)
      .where('quantite', '<=', 50) // Pré-filtre pour réduire les données
      .orderBy('quantite', 'asc')
      .limit(this.tableConfigs.stockAlerts.limit * 2); // Récupérer plus pour compenser le filtre

    const unsubscribe = query.onSnapshot(
      (snapshot) => this.handleStockAlertsUpdate(snapshot),
      (error) => this.handleError('stock-alerts', error)
    );

    this.listeners.set('stockAlerts', unsubscribe);
    console.log('✅ Listener alertes stock configuré (optimisé)');
  }

  /**
   * Listener pour matériel manquant (à commander)
   */
  setupMissingMaterialListener() {
    // Nettoyer l'ancien listener s'il existe
    if (this.listeners.has('missingMaterial')) {
      this.listeners.get('missingMaterial')();
    }

    const query = firebase.firestore()
      .collection('stocks')
      .where('aCommander', '==', true)
      .orderBy('updatedAt', 'desc')
      .limit(this.tableConfigs.missingMaterial.limit);

    const unsubscribe = query.onSnapshot(
      (snapshot) => this.handleMissingMaterialUpdate(snapshot),
      (error) => this.handleError('missing-material', error)
    );

    this.listeners.set('missingMaterial', unsubscribe);
    console.log('✅ Listener matériel manquant configuré');
  }

  /**
   * Listener pour emprunts en retard
   * Optimisé avec date de référence fixe pour éviter les requêtes trop fréquentes
   */
  setupOverdueEmpruntsListener() {
    // Nettoyer l'ancien listener s'il existe
    if (this.listeners.has('overdueEmprunts')) {
      this.listeners.get('overdueEmprunts')();
    }

    // Utiliser une date de référence arrondie à l'heure pour optimiser le cache
    const now = new Date();
    const referenceDate = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours());

    const query = firebase.firestore()
      .collection('emprunts')
      .where('statut', '==', 'Parti')
      .where('dateRetourPrevue', '<', referenceDate)
      .orderBy('dateRetourPrevue', 'asc')
      .limit(this.tableConfigs.overdueEmprunts.limit);

    const unsubscribe = query.onSnapshot(
      (snapshot) => this.handleOverdueEmpruntsUpdate(snapshot),
      (error) => this.handleError('overdue-emprunts', error)
    );

    this.listeners.set('overdueEmprunts', unsubscribe);
    console.log('✅ Listener emprunts en retard configuré (optimisé)');
  }

  /**
   * Listener pour prochains emprunts (< 30 jours)
   */
  setupUpcomingEmpruntsListener() {
    // Nettoyer l'ancien listener s'il existe
    if (this.listeners.has('upcomingEmprunts')) {
      this.listeners.get('upcomingEmprunts')();
    }

    const now = new Date();
    const in30Days = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);

    const query = firebase.firestore()
      .collection('emprunts')
      .where('statut', 'in', ['Pas prêt', 'Prêt'])
      .where('dateDepart', '>=', now)
      .where('dateDepart', '<=', in30Days)
      .orderBy('dateDepart', 'asc')
      .limit(this.tableConfigs.upcomingEmprunts.limit);

    const unsubscribe = query.onSnapshot(
      (snapshot) => this.handleUpcomingEmpruntsUpdate(snapshot),
      (error) => this.handleError('upcoming-emprunts', error)
    );

    this.listeners.set('upcomingEmprunts', unsubscribe);
    console.log('✅ Listener prochains emprunts configuré');
  }

  /**
   * Listener pour modules non opérationnels
   */
  setupNonOpModulesListener() {
    // Nettoyer l'ancien listener s'il existe
    if (this.listeners.has('nonOpModules')) {
      this.listeners.get('nonOpModules')();
    }

    const query = firebase.firestore()
      .collection('modules')
      .where('estPret', '==', false)
      .orderBy('updatedAt', 'desc')
      .limit(this.tableConfigs.nonOpModules.limit);

    const unsubscribe = query.onSnapshot(
      (snapshot) => this.handleNonOpModulesUpdate(snapshot),
      (error) => this.handleError('non-op-modules', error)
    );

    this.listeners.set('nonOpModules', unsubscribe);
    console.log('✅ Listener modules non-op configuré');
  }

  /**
   * Listener pour matériel non opérationnel
   */
  setupNonOpMaterialListener() {
    // Nettoyer l'ancien listener s'il existe
    if (this.listeners.has('nonOpMaterial')) {
      this.listeners.get('nonOpMaterial')();
    }

    const query = firebase.firestore()
      .collection('stocks')
      .where('estOperationnel', '==', false)
      .orderBy('updatedAt', 'desc')
      .limit(this.tableConfigs.nonOpMaterial.limit);

    const unsubscribe = query.onSnapshot(
      (snapshot) => this.handleNonOpMaterialUpdate(snapshot),
      (error) => this.handleError('non-op-material', error)
    );

    this.listeners.set('nonOpMaterial', unsubscribe);
    console.log('✅ Listener matériel non-op configuré');
  }

  /**
   * Listener pour emprunts en attente (non inventoriés/facturés)
   */
  setupPendingEmpruntsListener() {
    // Nettoyer les anciens listeners s'ils existent
    if (this.listeners.has('pendingEmpruntsInventory')) {
      this.listeners.get('pendingEmpruntsInventory')();
    }
    if (this.listeners.has('pendingEmpruntsBilling')) {
      this.listeners.get('pendingEmpruntsBilling')();
    }

    // Listener pour emprunts non inventoriés
    const nonInventoriedQuery = firebase.firestore()
      .collection('emprunts')
      .where('statut', '==', 'Revenu')
      .where('estInventorie', '==', false)
      .orderBy('dateRetourEffective', 'desc')
      .limit(10);

    const unsubscribe1 = nonInventoriedQuery.onSnapshot(
      (snapshot) => this.handlePendingEmpruntsUpdate(snapshot, 'inventaire'),
      (error) => this.handleError('pending-emprunts-inventory', error)
    );

    // Listener pour emprunts non facturés
    const nonBilledQuery = firebase.firestore()
      .collection('emprunts')
      .where('statut', '==', 'Revenu')
      .where('estFacture', '==', false)
      .orderBy('dateRetourEffective', 'desc')
      .limit(10);

    const unsubscribe2 = nonBilledQuery.onSnapshot(
      (snapshot) => this.handlePendingEmpruntsUpdate(snapshot, 'facturation'),
      (error) => this.handleError('pending-emprunts-billing', error)
    );

    this.listeners.set('pendingEmpruntsInventory', unsubscribe1);
    this.listeners.set('pendingEmpruntsBilling', unsubscribe2);
    console.log('✅ Listeners emprunts en attente configurés');
  }

  /**
   * Gestionnaire pour mise à jour alertes stock
   */
  handleStockAlertsUpdate(snapshot) {
    const startTime = Date.now();

    this.debounceUpdate('stockAlerts', () => {
      const alerts = snapshot.docs
        .map(doc => ({ id: doc.id, ...doc.data() }))
        .filter(stock => stock.quantite <= stock.seuilAlerte);

      // Monitoring des performances
      this.monitorQueryPerformance('stockAlerts', startTime, alerts.length);

      console.log(`📊 Alertes stock mises à jour: ${alerts.length} alertes`);

      this.dashboardUI.updateStockAlertsTable(alerts);
      this.updateRefreshIndicator('stockAlerts');
    }, 200);
  }

  /**
   * Gestionnaire pour mise à jour matériel manquant
   */
  handleMissingMaterialUpdate(snapshot) {
    const startTime = Date.now();

    this.debounceUpdate('missingMaterial', () => {
      const materials = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      // Monitoring des performances
      this.monitorQueryPerformance('missingMaterial', startTime, materials.length);

      console.log(`📦 Matériel manquant mis à jour: ${materials.length} articles`);

      this.dashboardUI.updateMissingMaterialTable(materials);
      this.updateRefreshIndicator('missingMaterial');
    }, 200);
  }

  /**
   * Gestionnaire pour mise à jour emprunts en retard
   */
  handleOverdueEmpruntsUpdate(snapshot) {
    const startTime = Date.now();

    this.debounceUpdate('overdueEmprunts', () => {
      const emprunts = snapshot.docs.map(doc => {
        const data = doc.data();
        const dateRetourPrevue = data.dateRetourPrevue.toDate();
        const daysOverdue = Math.floor((new Date() - dateRetourPrevue) / (1000 * 60 * 60 * 24));

        return {
          id: doc.id,
          ...data,
          dateRetourPrevue,
          daysOverdue
        };
      });

      // Monitoring des performances
      this.monitorQueryPerformance('overdueEmprunts', startTime, emprunts.length);

      console.log(`⏰ Emprunts en retard mis à jour: ${emprunts.length} emprunts`);

      this.dashboardUI.updateOverdueEmpruntsTable(emprunts);
      this.updateRefreshIndicator('overdueEmprunts');
    }, 200);
  }

  /**
   * Gestionnaire pour mise à jour prochains emprunts
   */
  handleUpcomingEmpruntsUpdate(snapshot) {
    this.debounceUpdate('upcomingEmprunts', () => {
      const emprunts = snapshot.docs.map(doc => {
        const data = doc.data();
        return {
          id: doc.id,
          ...data,
          dateDepart: data.dateDepart.toDate(),
          dateRetourPrevue: data.dateRetourPrevue.toDate()
        };
      });

      console.log(`📅 Prochains emprunts mis à jour: ${emprunts.length} emprunts`);

      this.dashboardUI.updateUpcomingEmpruntsTable(emprunts);
      this.updateRefreshIndicator('upcomingEmprunts');
    }, 200);
  }

  /**
   * Gestionnaire pour mise à jour modules non opérationnels
   */
  handleNonOpModulesUpdate(snapshot) {
    this.debounceUpdate('nonOpModules', () => {
      const modules = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      console.log(`📌 Modules non-op mis à jour: ${modules.length} modules`);

      this.dashboardUI.updateNonOpModulesTable(modules);
      this.updateRefreshIndicator('nonOpModules');
    }, 200);
  }

  /**
   * Gestionnaire pour mise à jour matériel non opérationnel
   */
  handleNonOpMaterialUpdate(snapshot) {
    this.debounceUpdate('nonOpMaterial', () => {
      const materials = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      }));

      console.log(`🔧 Matériel non-op mis à jour: ${materials.length} articles`);

      this.dashboardUI.updateNonOpMaterialTable(materials);
      this.updateRefreshIndicator('nonOpMaterial');
    }, 200);
  }

  /**
   * Gestionnaire pour mise à jour emprunts en attente
   */
  handlePendingEmpruntsUpdate(snapshot, type) {
    this.debounceUpdate(`pendingEmprunts_${type}`, () => {
      const emprunts = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data(),
        pendingType: type
      }));

      console.log(`⏳ Emprunts en attente (${type}) mis à jour: ${emprunts.length} emprunts`);

      // Fusionner avec l'autre type si nécessaire
      this.mergePendingEmprunts(emprunts, type);
      this.updateRefreshIndicator('pendingEmprunts');
    }, 200);
  }

  /**
   * Fusionner les emprunts en attente (inventaire + facturation)
   */
  mergePendingEmprunts(newEmprunts, type) {
    if (!this.pendingEmpruntsCache) {
      this.pendingEmpruntsCache = { inventaire: [], facturation: [] };
    }

    this.pendingEmpruntsCache[type] = newEmprunts;

    // Fusionner et dédupliquer
    const allPending = [...this.pendingEmpruntsCache.inventaire, ...this.pendingEmpruntsCache.facturation];
    const uniquePending = allPending.filter(
      (emprunt, index, self) => index === self.findIndex(e => e.id === emprunt.id)
    );

    this.dashboardUI.updatePendingEmpruntsTable(uniquePending.slice(0, 20));
  }

  /**
   * Initialiser la pagination pour tous les tableaux
   */
  initializePagination() {
    Object.keys(this.tableConfigs).forEach(tableId => {
      const config = this.tableConfigs[tableId];
      this.paginationManager.initializePagination(tableId, config.limit);
    });
  }

  /**
   * Debouncing pour éviter trop de mises à jour UI
   */
  debounceUpdate(key, callback, delay) {
    clearTimeout(this.debounceTimers.get(key));
    this.debounceTimers.set(key, setTimeout(callback, delay));
  }

  /**
   * Charger les données initiales via Cloud Function
   */
  async loadInitialData() {
    try {
      console.log('📊 Chargement des données initiales...');
      
      const getDashboardData = firebase.functions().httpsCallable('getDashboardData');
      const result = await getDashboardData();
      
      const data = result.data;
      console.log('✅ Données initiales chargées:', data.performance);
      
      // Afficher les métriques de performance
      this.displayPerformanceMetrics(data.performance);
      
    } catch (error) {
      console.error('❌ Erreur lors du chargement initial:', error);
      this.showError('Chargement initial', error.message);
    }
  }

  /**
   * Gérer les erreurs des listeners avec retry automatique
   */
  handleError(listenerId, error) {
    console.error(`❌ Erreur listener ${listenerId}:`, error);

    // Initialiser le compteur de retry s'il n'existe pas
    if (!this.retryCounters) {
      this.retryCounters = new Map();
    }

    const retryCount = this.retryCounters.get(listenerId) || 0;
    const maxRetries = 3;
    const retryDelay = Math.min(1000 * Math.pow(2, retryCount), 30000); // Backoff exponentiel, max 30s

    if (retryCount < maxRetries) {
      this.retryCounters.set(listenerId, retryCount + 1);

      console.log(`🔄 Tentative de reconnexion ${retryCount + 1}/${maxRetries} pour ${listenerId} dans ${retryDelay}ms...`);

      // Afficher une notification d'erreur temporaire
      this.showError(`Reconnexion ${listenerId}`, `Tentative ${retryCount + 1}/${maxRetries}...`);

      // Retry avec backoff exponentiel
      setTimeout(() => {
        this.retryListener(listenerId);
      }, retryDelay);

    } else {
      // Max retries atteint
      console.error(`💥 Échec définitif du listener ${listenerId} après ${maxRetries} tentatives`);
      this.showError(`Erreur ${listenerId}`, 'Connexion impossible. Veuillez recharger la page.');

      // Marquer le listener comme en échec
      this.failedListeners = this.failedListeners || new Set();
      this.failedListeners.add(listenerId);
    }
  }

  /**
   * Retry un listener spécifique
   */
  retryListener(listenerId) {
    try {
      switch (listenerId) {
        case 'stock-alerts':
          this.setupStockAlertsListener();
          break;
        case 'missing-material':
          this.setupMissingMaterialListener();
          break;
        case 'overdue-emprunts':
          this.setupOverdueEmpruntsListener();
          break;
        case 'upcoming-emprunts':
          this.setupUpcomingEmpruntsListener();
          break;
        case 'non-op-modules':
          this.setupNonOpModulesListener();
          break;
        case 'non-op-material':
          this.setupNonOpMaterialListener();
          break;
        case 'pending-emprunts-inventory':
        case 'pending-emprunts-billing':
          this.setupPendingEmpruntsListener();
          break;
        default:
          console.warn(`Listener inconnu pour retry: ${listenerId}`);
      }

      // Reset le compteur en cas de succès
      if (this.retryCounters) {
        this.retryCounters.delete(listenerId);
      }

      console.log(`✅ Reconnexion réussie pour ${listenerId}`);

    } catch (retryError) {
      console.error(`❌ Erreur lors du retry de ${listenerId}:`, retryError);
      this.handleError(listenerId, retryError);
    }
  }

  /**
   * Surveiller les performances des requêtes
   */
  monitorQueryPerformance(tableId, startTime, itemCount) {
    const duration = Date.now() - startTime;

    if (!this.performanceMetrics) {
      this.performanceMetrics = new Map();
    }

    const metrics = this.performanceMetrics.get(tableId) || {
      totalQueries: 0,
      totalDuration: 0,
      averageDuration: 0,
      lastUpdate: null,
      itemCounts: []
    };

    metrics.totalQueries++;
    metrics.totalDuration += duration;
    metrics.averageDuration = metrics.totalDuration / metrics.totalQueries;
    metrics.lastUpdate = new Date();
    metrics.itemCounts.push(itemCount);

    // Garder seulement les 10 dernières mesures
    if (metrics.itemCounts.length > 10) {
      metrics.itemCounts.shift();
    }

    this.performanceMetrics.set(tableId, metrics);

    // Alerter si performance dégradée
    if (duration > 2000) {
      console.warn(`⚠️ Requête lente pour ${tableId}: ${duration}ms`);
    }

    return metrics;
  }

  /**
   * Obtenir un rapport de performance global
   */
  getPerformanceReport() {
    if (!this.performanceMetrics) {
      return { message: 'Aucune métrique disponible' };
    }

    const report = {};
    this.performanceMetrics.forEach((metrics, tableId) => {
      report[tableId] = {
        averageDuration: Math.round(metrics.averageDuration),
        totalQueries: metrics.totalQueries,
        lastUpdate: metrics.lastUpdate,
        averageItemCount: Math.round(
          metrics.itemCounts.reduce((a, b) => a + b, 0) / metrics.itemCounts.length
        )
      };
    });

    return report;
  }

  /**
   * Vérifier la santé des listeners
   */
  checkListenersHealth() {
    const expectedListeners = Object.keys(this.tableConfigs);
    const activeListeners = Array.from(this.listeners.keys());
    const failedListeners = this.failedListeners ? Array.from(this.failedListeners) : [];

    console.log('🔍 État des listeners:', {
      expected: expectedListeners.length,
      active: activeListeners.length,
      failed: failedListeners.length,
      missing: expectedListeners.filter(id => !activeListeners.includes(id))
    });

    return {
      healthy: activeListeners.length === expectedListeners.length && failedListeners.length === 0,
      activeCount: activeListeners.length,
      expectedCount: expectedListeners.length,
      failedCount: failedListeners.length
    };
  }

  /**
   * Nettoyer tous les listeners
   */
  cleanup() {
    console.log('🧹 Nettoyage des listeners...');

    this.listeners.forEach((unsubscribe, key) => {
      if (typeof unsubscribe === 'function') {
        unsubscribe();
      }
    });

    this.listeners.clear();
    this.debounceTimers.clear();

    // Nettoyer les compteurs de retry
    if (this.retryCounters) {
      this.retryCounters.clear();
    }

    console.log('✅ Nettoyage terminé');
  }

  /**
   * Initialiser l'indicateur de rafraîchissement
   */
  initializeRefreshIndicator() {
    this.refreshIndicator = document.getElementById('refresh-indicator');
    if (this.refreshIndicator) {
      this.refreshIndicator.innerHTML = '🔄 Temps-réel actif';
      this.refreshIndicator.className = 'refresh-indicator active';
    }
  }

  /**
   * Mettre à jour l'indicateur de rafraîchissement
   */
  updateRefreshIndicator(tableId) {
    if (this.refreshIndicator) {
      this.refreshIndicator.innerHTML = `🔄 Mis à jour: ${new Date().toLocaleTimeString()}`;
      this.refreshIndicator.className = 'refresh-indicator updated';
      
      // Retour à l'état normal après 2 secondes
      setTimeout(() => {
        this.refreshIndicator.className = 'refresh-indicator active';
      }, 2000);
    }
  }

  /**
   * Afficher une erreur à l'utilisateur
   */
  showError(title, message) {
    // Ici, on pourrait utiliser une bibliothèque de notifications
    console.error(`${title}: ${message}`);
    
    // Affichage simple pour le moment
    const errorContainer = document.getElementById('error-container');
    if (errorContainer) {
      errorContainer.innerHTML = `
        <div class="alert alert-error">
          <strong>${title}</strong>: ${message}
        </div>
      `;
      
      // Masquer après 5 secondes
      setTimeout(() => {
        errorContainer.innerHTML = '';
      }, 5000);
    }
  }

  /**
   * Afficher les métriques de performance
   */
  displayPerformanceMetrics(performance) {
    const metricsContainer = document.getElementById('performance-metrics');
    if (metricsContainer && performance) {
      metricsContainer.innerHTML = `
        <div class="metrics">
          <span>⏱️ ${performance.executionTimeMs}ms</span>
          <span>📊 ${performance.totalQueries} requêtes</span>
        </div>
      `;
    }
  }

  /**
   * Rafraîchir un tableau spécifique
   */
  refreshTable(tableId) {
    console.log(`🔄 Rafraîchissement du tableau: ${tableId}`);

    if (!this.isInitialized) {
      console.warn('Dashboard non initialisé');
      return;
    }

    // Afficher l'indicateur de rafraîchissement
    this.updateRefreshIndicator(tableId);

    // Déclencher une nouvelle requête selon le type de tableau
    switch (tableId) {
      case 'stockAlerts':
        this.setupStockAlertsListener();
        break;
      case 'missingMaterial':
        this.setupMissingMaterialListener();
        break;
      case 'overdueEmprunts':
        this.setupOverdueEmpruntsListener();
        break;
      case 'upcomingEmprunts':
        this.setupUpcomingEmpruntsListener();
        break;
      case 'nonOpModules':
        this.setupNonOpModulesListener();
        break;
      case 'nonOpMaterial':
        this.setupNonOpMaterialListener();
        break;
      case 'pendingEmprunts':
        this.setupPendingEmpruntsListener();
        break;
      default:
        console.warn(`Tableau inconnu: ${tableId}`);
    }
  }

  /**
   * Rafraîchir tous les tableaux
   */
  refreshAllTables() {
    console.log('🔄 Rafraîchissement de tous les tableaux');

    if (!this.isInitialized) {
      console.warn('Dashboard non initialisé');
      return;
    }

    // Rafraîchir chaque tableau
    Object.keys(this.tableConfigs).forEach(tableId => {
      this.refreshTable(tableId);
    });

    // Recharger les données initiales via Cloud Function
    this.loadInitialData();
  }
}

// Export pour utilisation globale
window.DashboardManager = DashboardManager;
