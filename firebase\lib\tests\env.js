"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * Variables d'environnement pour les tests (émulateurs).
 * Laisse firebase-tools surcharger si déjà présent (emulators:exec).
 */
process.env.FIRESTORE_EMULATOR_HOST = process.env.FIRESTORE_EMULATOR_HOST || '127.0.0.1:8080';
process.env.FIREBASE_AUTH_EMULATOR_HOST = process.env.FIREBASE_AUTH_EMULATOR_HOST || '127.0.0.1:9099';
// Ne pas définir STORAGE si l'émulateur n'est pas lancé
process.env.FUNCTIONS_EMULATOR_HOST = process.env.FUNCTIONS_EMULATOR_HOST || '127.0.0.1:5001';
process.env.GCLOUD_PROJECT = process.env.GCLOUD_PROJECT || 'sigma-nova';
//# sourceMappingURL=env.js.map