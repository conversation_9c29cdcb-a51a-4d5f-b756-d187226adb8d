/**
 * SIGMA - Système Informatique de Gestion du Matériel
 * Code principal Google Apps Script
 */

/**
 * Fonction utilitaire pour inclure le contenu d'autres fichiers (CSS, JS) dans le HTML.
 * C'est la méthode standard pour servir des applications web complètes sur GAS.
 */
function include(filename) {
  return HtmlService.createHtmlOutputFromFile(filename).getContent();
}

function doGet(e) {
  try {
    const page = e.parameter.page;
    console.log(`📄 Requête reçue pour la page: ${page || 'par défaut (dashboard)'}`);

    if (page === 'login') {
      return serveLoginPage();
    }
    // L'admin/users est une page spécifique
    if (page === 'admin/users') {
        return serveAdminUsersPage();
    }
    // Par défaut, servir le dashboard.
    return serveDashboard();

  } catch (error) {
    console.error('❌ Erreur dans doGet:', error);
    return serveErrorPage(error.message);
  }
}

function serveLoginPage() {
  return HtmlService.createTemplateFromFile('html/login')
      .evaluate()
      .setTitle('SIGMA - Connexion')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
      .addMetaTag('viewport', 'width=device-width, initial-scale=1.0');
}

function serveDashboard() {
  return HtmlService.createTemplateFromFile('html/dashboard')
      .evaluate()
      .setTitle('SIGMA - Dashboard')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
      .addMetaTag('viewport', 'width=device-width, initial-scale=1.0');
}

function serveAdminUsersPage() {
    return HtmlService.createTemplateFromFile('html/admin/users')
        .evaluate()
        .setTitle('SIGMA - Admin Utilisateurs')
        .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL)
        .addMetaTag('viewport', 'width=device-width, initial-scale=1.0');
}

function serveErrorPage(errorMessage) {
  const html = `<!DOCTYPE html><html><head><title>SIGMA - Erreur</title></head><body><h1>⚠️ Erreur</h1><p>${errorMessage}</p></body></html>`;
  return HtmlService.createHtmlOutput(html)
      .setTitle('SIGMA - Erreur')
      .setXFrameOptionsMode(HtmlService.XFrameOptionsMode.ALLOWALL);
}
