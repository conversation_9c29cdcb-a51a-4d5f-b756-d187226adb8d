/**
 * Tests unitaires pour DashboardManager
 * Validation des gestionnaires de mise à jour et des listeners Firestore
 */

describe('DashboardManager', () => {
  let dashboardManager;
  let mockDashboardUI;
  let mockFirestore;
  let mockSnapshot;

  beforeEach(() => {
    // Mock DashboardUI
    mockDashboardUI = {
      updateStockAlertsTable: jest.fn(),
      updateMissingMaterialTable: jest.fn(),
      updateOverdueEmpruntsTable: jest.fn(),
      updateUpcomingEmpruntsTable: jest.fn(),
      updateNonOpModulesTable: jest.fn(),
      updateNonOpMaterialTable: jest.fn(),
      updatePendingEmpruntsTable: jest.fn()
    };

    // Mock PaginationManager
    global.PaginationManager = jest.fn().mockImplementation(() => ({
      initializePagination: jest.fn(),
      cleanup: jest.fn()
    }));

    // Mock DashboardUI constructor
    global.DashboardUI = jest.fn().mockImplementation(() => mockDashboardUI);

    // Mock Firebase
    mockSnapshot = {
      docs: [
        {
          id: 'test1',
          data: () => ({
            nom: 'Test Stock 1',
            quantite: 5,
            seuilAlerte: 10,
            estOperationnel: true
          })
        },
        {
          id: 'test2', 
          data: () => ({
            nom: 'Test Stock 2',
            quantite: 15,
            seuilAlerte: 20,
            estOperationnel: true
          })
        }
      ]
    };

    mockFirestore = {
      collection: jest.fn().mockReturnThis(),
      where: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      onSnapshot: jest.fn()
    };

    global.firebase = {
      firestore: () => mockFirestore,
      auth: () => ({
        currentUser: { uid: 'test-user' }
      })
    };

    // Créer une instance de DashboardManager
    dashboardManager = new DashboardManager();
    dashboardManager.dashboardUI = mockDashboardUI;
    dashboardManager.isInitialized = true;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('handleStockAlertsUpdate', () => {
    it('devrait filtrer et afficher les alertes stock correctement', (done) => {
      // Simuler un snapshot avec des stocks
      dashboardManager.handleStockAlertsUpdate(mockSnapshot);

      // Attendre que le debounce se termine
      setTimeout(() => {
        expect(mockDashboardUI.updateStockAlertsTable).toHaveBeenCalledWith([
          {
            id: 'test1',
            nom: 'Test Stock 1',
            quantite: 5,
            seuilAlerte: 10,
            estOperationnel: true
          }
        ]);
        done();
      }, 250);
    });

    it('devrait monitorer les performances', (done) => {
      dashboardManager.handleStockAlertsUpdate(mockSnapshot);

      setTimeout(() => {
        expect(dashboardManager.performanceMetrics).toBeDefined();
        expect(dashboardManager.performanceMetrics.has('stockAlerts')).toBe(true);
        done();
      }, 250);
    });
  });

  describe('handleMissingMaterialUpdate', () => {
    it('devrait traiter les matériaux manquants', (done) => {
      const mockMaterialSnapshot = {
        docs: [
          {
            id: 'material1',
            data: () => ({
              nom: 'Matériel Test',
              aCommander: true,
              updatedAt: new Date()
            })
          }
        ]
      };

      dashboardManager.handleMissingMaterialUpdate(mockMaterialSnapshot);

      setTimeout(() => {
        expect(mockDashboardUI.updateMissingMaterialTable).toHaveBeenCalledWith([
          {
            id: 'material1',
            nom: 'Matériel Test',
            aCommander: true,
            updatedAt: expect.any(Date)
          }
        ]);
        done();
      }, 250);
    });
  });

  describe('handleOverdueEmpruntsUpdate', () => {
    it('devrait calculer les jours de retard correctement', (done) => {
      const pastDate = new Date();
      pastDate.setDate(pastDate.getDate() - 5); // 5 jours en retard

      const mockEmpruntSnapshot = {
        docs: [
          {
            id: 'emprunt1',
            data: () => ({
              nom: 'Emprunt Test',
              statut: 'Parti',
              dateRetourPrevue: {
                toDate: () => pastDate
              },
              emprunteur: 'Test User'
            })
          }
        ]
      };

      dashboardManager.handleOverdueEmpruntsUpdate(mockEmpruntSnapshot);

      setTimeout(() => {
        const expectedCall = mockDashboardUI.updateOverdueEmpruntsTable.mock.calls[0][0];
        expect(expectedCall[0].daysOverdue).toBe(5);
        expect(expectedCall[0].dateRetourPrevue).toEqual(pastDate);
        done();
      }, 250);
    });
  });

  describe('refreshTable', () => {
    it('devrait configurer le bon listener selon le tableId', () => {
      const setupSpy = jest.spyOn(dashboardManager, 'setupStockAlertsListener');
      
      dashboardManager.refreshTable('stockAlerts');
      
      expect(setupSpy).toHaveBeenCalled();
    });

    it('devrait afficher un warning pour un tableau inconnu', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      
      dashboardManager.refreshTable('unknownTable');
      
      expect(consoleSpy).toHaveBeenCalledWith('Tableau inconnu: unknownTable');
      consoleSpy.mockRestore();
    });
  });

  describe('checkListenersHealth', () => {
    it('devrait retourner l\'état de santé des listeners', () => {
      // Simuler des listeners actifs
      dashboardManager.listeners.set('stockAlerts', jest.fn());
      dashboardManager.listeners.set('missingMaterial', jest.fn());

      const health = dashboardManager.checkListenersHealth();

      expect(health).toEqual({
        healthy: false, // Pas tous les listeners attendus
        activeCount: 2,
        expectedCount: 7, // 7 tableaux configurés
        failedCount: 0
      });
    });
  });

  describe('monitorQueryPerformance', () => {
    it('devrait enregistrer les métriques de performance', () => {
      const startTime = Date.now() - 100; // 100ms ago
      
      const metrics = dashboardManager.monitorQueryPerformance('stockAlerts', startTime, 5);
      
      expect(metrics.totalQueries).toBe(1);
      expect(metrics.averageDuration).toBeGreaterThan(90);
      expect(metrics.itemCounts).toEqual([5]);
    });

    it('devrait alerter pour les requêtes lentes', () => {
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      const startTime = Date.now() - 3000; // 3 secondes ago
      
      dashboardManager.monitorQueryPerformance('stockAlerts', startTime, 5);
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Requête lente pour stockAlerts')
      );
      consoleSpy.mockRestore();
    });
  });

  describe('handleError', () => {
    it('devrait implémenter le retry avec backoff exponentiel', () => {
      const setTimeoutSpy = jest.spyOn(global, 'setTimeout');
      
      dashboardManager.handleError('test-listener', new Error('Test error'));
      
      expect(setTimeoutSpy).toHaveBeenCalled();
      const delay = setTimeoutSpy.mock.calls[0][1];
      expect(delay).toBe(1000); // Premier retry à 1s
      
      setTimeoutSpy.mockRestore();
    });

    it('devrait arrêter après le nombre max de retries', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      // Simuler 3 échecs
      dashboardManager.retryCounters = new Map();
      dashboardManager.retryCounters.set('test-listener', 3);
      
      dashboardManager.handleError('test-listener', new Error('Test error'));
      
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Échec définitif du listener test-listener')
      );
      consoleSpy.mockRestore();
    });
  });
});
