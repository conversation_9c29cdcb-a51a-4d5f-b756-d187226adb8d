{"indexes": [{"collectionGroup": "emprunts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "statut", "order": "ASCENDING"}, {"fieldPath": "dateRetourPrevue", "order": "ASCENDING"}]}, {"collectionGroup": "emprunts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "utilisateurId", "order": "ASCENDING"}, {"fieldPath": "dateCreation", "order": "DESCENDING"}]}, {"collectionGroup": "emprunts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "statut", "order": "ASCENDING"}, {"fieldPath": "date<PERSON><PERSON><PERSON>", "order": "ASCENDING"}]}, {"collectionGroup": "emprunts", "queryScope": "COLLECTION", "fields": [{"fieldPath": "estInventorie", "order": "ASCENDING"}, {"fieldPath": "estFacture", "order": "ASCENDING"}, {"fieldPath": "statut", "order": "ASCENDING"}]}, {"collectionGroup": "stocks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "quantite", "order": "ASCENDING"}, {"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "estOperationnel", "order": "ASCENDING"}]}, {"collectionGroup": "stocks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "estOperationnel", "order": "ASCENDING"}, {"fieldPath": "quantite", "order": "ASCENDING"}]}, {"collectionGroup": "stocks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "stocks", "queryScope": "COLLECTION", "fields": [{"fieldPath": "estOperationnel", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}, {"collectionGroup": "modules", "queryScope": "COLLECTION", "fields": [{"fieldPath": "estPret", "order": "ASCENDING"}, {"fieldPath": "updatedAt", "order": "DESCENDING"}]}], "fieldOverrides": []}