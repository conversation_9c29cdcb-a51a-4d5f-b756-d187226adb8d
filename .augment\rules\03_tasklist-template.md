---

type: manual

title: "Template Tasklist SIGMA (JSON)"

description: "Modèle complet de tasklist JSON à insérer à la demande."

---



```json

{

&nbsp; "tasklist": \[

&nbsp;   {

&nbsp;     "phase": "Analyse",

&nbsp;     "description": "Comprendre la fonctionnalité, identifier les cas d’usage.",

&nbsp;     "tasks": \[

&nbsp;       "Écrire le résumé de la fonctionnalité",

&nbsp;       "Lister les critères d’acceptation"

&nbsp;     ]

&nbsp;   },

&nbsp;   {

&nbsp;     "phase": "Implémentation",

&nbsp;     "description": "Décomposer en sous-tâches codables, générer test initial.",

&nbsp;     "tasks": \[

&nbsp;       "Créer le squelette de la première fonction",

&nbsp;       "Écrire un test unitaire basique"

&nbsp;     ]

&nbsp;   },

&nbsp;   {

&nbsp;     "phase": "Validation",

&nbsp;     "description": "Vérifier l’implémentation, exécuter les tests, corriger les erreurs.",

&nbsp;     "tasks": \[

&nbsp;       "Exécuter la suite de tests complète",

&nbsp;       "Corriger les anomalies détectées"

&nbsp;     ]

&nbsp;   }

&nbsp; ]

}



