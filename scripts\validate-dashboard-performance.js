#!/usr/bin/env node

/**
 * Script de validation des performances du dashboard SIGMA
 * Mesure les temps de réponse et valide les critères de performance
 */

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('⚡ Validation des performances du dashboard SIGMA...\n');

// Critères de performance requis
const PERFORMANCE_CRITERIA = {
  initialLoad: 2000,      // < 2 secondes
  listenerSetup: 1000,    // < 1 seconde
  dataUpdate: 500,        // < 500ms
  memoryUsage: 50 * 1024 * 1024, // < 50MB
  firestoreReads: 100     // < 100 lectures/minute
};

/**
 * Exécuter un test de performance avec Lighthouse
 */
async function runLighthouseTest() {
  console.log('🔍 Exécution de l\'audit Lighthouse...');
  
  try {
    // Générer un rapport Lighthouse
    const lighthouseCmd = `npx lighthouse http://127.0.0.1:8080/html/dashboard.html ` +
      `--output=json --output-path=./test-results/lighthouse-dashboard.json ` +
      `--chrome-flags="--headless" --quiet`;
    
    execSync(lighthouseCmd, { stdio: 'pipe' });
    
    // Lire le rapport
    const reportPath = path.join(__dirname, '..', 'test-results', 'lighthouse-dashboard.json');
    if (fs.existsSync(reportPath)) {
      const report = JSON.parse(fs.readFileSync(reportPath, 'utf8'));
      
      return {
        performance: Math.round(report.lhr.categories.performance.score * 100),
        fcp: report.lhr.audits['first-contentful-paint'].numericValue,
        lcp: report.lhr.audits['largest-contentful-paint'].numericValue,
        tti: report.lhr.audits['interactive'].numericValue,
        cls: report.lhr.audits['cumulative-layout-shift'].numericValue,
        tbt: report.lhr.audits['total-blocking-time'].numericValue
      };
    }
  } catch (error) {
    console.warn('⚠️ Lighthouse non disponible, utilisation des métriques alternatives');
    return null;
  }
}

/**
 * Tester les performances avec Playwright
 */
async function runPlaywrightPerformanceTest() {
  console.log('🎭 Test de performance avec Playwright...');
  
  const testScript = `
    const { test, expect } = require('@playwright/test');
    
    test('Performance Dashboard', async ({ page }) => {
      const startTime = Date.now();
      
      // Aller au dashboard
      await page.goto('http://127.0.0.1:8080/html/dashboard.html');
      
      // Attendre l'initialisation complète
      await page.waitForFunction(() => {
        return window.dashboardManager && window.dashboardManager.isInitialized;
      }, { timeout: 5000 });
      
      const loadTime = Date.now() - startTime;
      
      // Mesurer l'utilisation mémoire
      const memoryUsage = await page.evaluate(() => {
        return (performance.memory && performance.memory.usedJSHeapSize) || 0;
      });
      
      // Vérifier la santé des listeners
      const listenersHealth = await page.evaluate(() => {
        return window.dashboardManager.checkListenersHealth();
      });
      
      // Mesurer le temps de mise à jour
      const updateStartTime = Date.now();
      await page.evaluate(() => {
        window.dashboardManager.refreshAllTables();
      });
      
      await page.waitForTimeout(1000);
      const updateTime = Date.now() - updateStartTime;
      
      console.log(JSON.stringify({
        loadTime,
        memoryUsage,
        updateTime,
        activeListeners: listenersHealth.activeCount,
        expectedListeners: listenersHealth.expectedCount,
        healthy: listenersHealth.healthy
      }));
    });
  `;
  
  // Écrire le script de test temporaire
  const testPath = path.join(__dirname, 'temp-performance-test.js');
  fs.writeFileSync(testPath, testScript);
  
  try {
    // Exécuter le test
    const result = execSync(`npx playwright test ${testPath} --reporter=line`, {
      encoding: 'utf8',
      cwd: path.join(__dirname, '..')
    });
    
    // Extraire les métriques du résultat
    const metricsMatch = result.match(/\{[^}]+\}/);
    if (metricsMatch) {
      return JSON.parse(metricsMatch[0]);
    }
    
  } catch (error) {
    console.warn('⚠️ Erreur lors du test Playwright:', error.message);
  } finally {
    // Nettoyer le fichier temporaire
    if (fs.existsSync(testPath)) {
      fs.unlinkSync(testPath);
    }
  }
  
  return null;
}

/**
 * Générer le rapport de performance
 */
function generatePerformanceReport(lighthouseMetrics, playwrightMetrics) {
  console.log('\n📊 RAPPORT DE PERFORMANCE DASHBOARD SIGMA');
  console.log('=' .repeat(50));
  
  const report = {
    timestamp: new Date().toISOString(),
    criteria: PERFORMANCE_CRITERIA,
    results: {},
    passed: true
  };
  
  // Métriques Lighthouse
  if (lighthouseMetrics) {
    console.log('\n🔍 Métriques Lighthouse:');
    console.log(`  Performance Score: ${lighthouseMetrics.performance}%`);
    console.log(`  First Contentful Paint: ${Math.round(lighthouseMetrics.fcp)}ms`);
    console.log(`  Largest Contentful Paint: ${Math.round(lighthouseMetrics.lcp)}ms`);
    console.log(`  Time to Interactive: ${Math.round(lighthouseMetrics.tti)}ms`);
    console.log(`  Cumulative Layout Shift: ${lighthouseMetrics.cls.toFixed(3)}`);
    console.log(`  Total Blocking Time: ${Math.round(lighthouseMetrics.tbt)}ms`);
    
    report.results.lighthouse = lighthouseMetrics;
    
    // Validation des critères
    if (lighthouseMetrics.performance < 90) {
      console.log('  ❌ Performance Score < 90%');
      report.passed = false;
    } else {
      console.log('  ✅ Performance Score ≥ 90%');
    }
  }
  
  // Métriques Playwright
  if (playwrightMetrics) {
    console.log('\n🎭 Métriques Playwright:');
    console.log(`  Temps de chargement: ${playwrightMetrics.loadTime}ms`);
    console.log(`  Temps de mise à jour: ${playwrightMetrics.updateTime}ms`);
    console.log(`  Utilisation mémoire: ${Math.round(playwrightMetrics.memoryUsage / 1024 / 1024)}MB`);
    console.log(`  Listeners actifs: ${playwrightMetrics.activeListeners}/${playwrightMetrics.expectedListeners}`);
    console.log(`  Santé globale: ${playwrightMetrics.healthy ? '✅' : '❌'}`);
    
    report.results.playwright = playwrightMetrics;
    
    // Validation des critères
    const validations = [
      {
        name: 'Chargement initial',
        value: playwrightMetrics.loadTime,
        threshold: PERFORMANCE_CRITERIA.initialLoad,
        unit: 'ms'
      },
      {
        name: 'Mise à jour données',
        value: playwrightMetrics.updateTime,
        threshold: PERFORMANCE_CRITERIA.dataUpdate,
        unit: 'ms'
      },
      {
        name: 'Utilisation mémoire',
        value: playwrightMetrics.memoryUsage,
        threshold: PERFORMANCE_CRITERIA.memoryUsage,
        unit: 'bytes'
      }
    ];
    
    console.log('\n📋 Validation des critères:');
    validations.forEach(validation => {
      const passed = validation.value <= validation.threshold;
      const status = passed ? '✅' : '❌';
      const displayValue = validation.unit === 'bytes' 
        ? `${Math.round(validation.value / 1024 / 1024)}MB`
        : `${validation.value}${validation.unit}`;
      const displayThreshold = validation.unit === 'bytes'
        ? `${Math.round(validation.threshold / 1024 / 1024)}MB`
        : `${validation.threshold}${validation.unit}`;
      
      console.log(`  ${status} ${validation.name}: ${displayValue} (seuil: ${displayThreshold})`);
      
      if (!passed) {
        report.passed = false;
      }
    });
    
    if (!playwrightMetrics.healthy) {
      console.log('  ❌ Santé des listeners: Certains listeners ne sont pas actifs');
      report.passed = false;
    } else {
      console.log('  ✅ Santé des listeners: Tous les listeners sont actifs');
    }
  }
  
  // Résultat final
  console.log('\n' + '='.repeat(50));
  if (report.passed) {
    console.log('🎉 VALIDATION RÉUSSIE - Toutes les performances respectent les critères');
  } else {
    console.log('⚠️ VALIDATION ÉCHOUÉE - Certains critères ne sont pas respectés');
  }
  console.log('='.repeat(50));
  
  // Sauvegarder le rapport
  const reportPath = path.join(__dirname, '..', 'test-results', 'performance-report.json');
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  console.log(`\n📄 Rapport sauvegardé: ${reportPath}`);
  
  return report.passed;
}

/**
 * Fonction principale
 */
async function main() {
  try {
    // Créer le dossier de résultats
    const resultsDir = path.join(__dirname, '..', 'test-results');
    if (!fs.existsSync(resultsDir)) {
      fs.mkdirSync(resultsDir, { recursive: true });
    }
    
    // Démarrer le serveur HTTP
    console.log('🚀 Démarrage du serveur de test...');
    const serverProcess = execSync('npx http-server ./src -p 8080 --cors &', {
      stdio: 'pipe',
      cwd: path.join(__dirname, '..')
    });
    
    // Attendre que le serveur soit prêt
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Exécuter les tests de performance
    const lighthouseMetrics = await runLighthouseTest();
    const playwrightMetrics = await runPlaywrightPerformanceTest();
    
    // Générer le rapport
    const passed = generatePerformanceReport(lighthouseMetrics, playwrightMetrics);
    
    process.exit(passed ? 0 : 1);
    
  } catch (error) {
    console.error('\n❌ Erreur lors de la validation:', error.message);
    process.exit(1);
  }
}

// Exécuter si appelé directement
if (require.main === module) {
  main();
}
